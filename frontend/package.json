{"name": "nextjs-shadcn", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -H 0.0.0.0 -p 9001 --turbopack", "build": "node scripts/generate-research-data.js && next build", "start": "next start", "lint": "bunx biome lint --write && bunx tsc --noEmit", "format": "bunx biome format --write", "copy-assets": "node scripts/copy-research-assets.js", "generate-research": "node scripts/generate-research-data.js"}, "dependencies": {"@aws-amplify/ui-react": "^6.11.2", "@aws-sdk/client-cognito-identity-provider": "^3.821.0", "@aws-sdk/client-dynamodb": "^3.821.0", "@aws-sdk/lib-dynamodb": "^3.821.0", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-slot": "^1.2.2", "@stripe/stripe-js": "^7.3.1", "aws-amplify": "^6.15.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "gray-matter": "^4.0.3", "jwt-decode": "^4.0.0", "lucide-react": "^0.475.0", "next": "^15.2.0", "oidc-client-ts": "^3.2.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^10.1.0", "react-oidc-context": "^3.3.0", "stripe": "^18.2.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "eslint": "^9", "eslint-config-next": "15.1.7", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}