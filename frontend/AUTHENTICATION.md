# AWS Cognito Authentication System

## Overview

This document describes the complete AWS Cognito authentication system implemented for the Gcandle Intelligence frontend. The system is designed for **static export compatibility** with Next.js and S3 deployment, using AWS Cognito Hosted UI for authentication.

## Architecture

### Core Components

```
📁 Authentication System
├── 🔧 Core Configuration
│   ├── lib/auth/amplify-config.ts     # AWS Amplify setup
│   ├── lib/auth/token-manager.ts      # Secure token handling
│   └── lib/auth/auth-utils.ts         # Helper functions
├── 🎣 React Hooks & Context
│   ├── contexts/AuthContext.tsx       # Global auth state
│   ├── hooks/useAuth.ts              # Main auth hook
│   └── hooks/useRouteProtection.ts   # Client-side route protection
├── 🧩 UI Components
│   ├── components/header.tsx         # Navigation with auth
│   └── app/ClientBody.tsx           # Auth provider wrapper
└── 📄 Authentication Pages
    ├── app/auth/callback/page.tsx    # OAuth callback handler
    ├── app/signin/page.tsx          # Sign in page
    └── app/signup/page.tsx          # Sign up page
```

## Key Features

### ✅ Static Export Compatible
- **No middleware**: Removed incompatible middleware for static export
- **No API routes**: All authentication handled client-side
- **Client-side routing**: Route protection without server-side logic

### ✅ Secure Token Management
- **JWT token handling**: Automatic token acquisition and validation
- **Secure storage**: localStorage with proper error handling
- **Automatic refresh**: Background token renewal
- **Expiration checking**: Proactive token validation

### ✅ Modern React Patterns
- **Context API**: Global authentication state management
- **Custom hooks**: Reusable authentication logic
- **Suspense boundaries**: Proper loading states
- **TypeScript**: Full type safety

## Environment Variables

Create a `.env.local` file with the following variables:

```bash
# AWS Cognito Configuration
NEXT_PUBLIC_COGNITO_USER_POOL_ID=us-east-1_xxxxxxxxx
NEXT_PUBLIC_COGNITO_CLIENT_ID=xxxxxxxxxxxxxxxxxxxxxxxxxx
NEXT_PUBLIC_COGNITO_DOMAIN=your-domain.auth.us-east-1.amazoncognito.com

# Redirect URLs (comma-separated for multiple environments)
NEXT_PUBLIC_REDIRECT_SIGN_IN=http://localhost:9000/auth/callback,https://yourdomain.com/auth/callback
NEXT_PUBLIC_REDIRECT_SIGN_OUT=http://localhost:9000,https://yourdomain.com

# Optional: API and Storage
NEXT_PUBLIC_API_ENDPOINT=https://api.yourdomain.com
NEXT_PUBLIC_S3_BUCKET=your-bucket-name
NEXT_PUBLIC_S3_REGION=us-east-1
```

## Usage

### Basic Authentication

```tsx
import { useAuth } from '@/hooks/useAuth';

function MyComponent() {
  const { isAuthenticated, isLoading, user, signOut } = useAuth();

  if (isLoading) return <div>Loading...</div>;

  if (!isAuthenticated) {
    return <div>Please sign in</div>;
  }

  return (
    <div>
      <h1>Welcome, {user?.name}!</h1>
      <button onClick={signOut}>Sign Out</button>
    </div>
  );
}
```

### Route Protection

```tsx
import { ProtectedRoute } from '@/hooks/useRouteProtection';

function ProtectedPage() {
  return (
    <ProtectedRoute>
      <div>This content is only visible to authenticated users</div>
    </ProtectedRoute>
  );
}
```

### Manual Sign In/Sign Up

```tsx
import { redirectToSignIn, redirectToSignUp } from '@/lib/auth/auth-utils';

function AuthButtons() {
  return (
    <div>
      <button onClick={redirectToSignIn}>Sign In</button>
      <button onClick={redirectToSignUp}>Sign Up</button>
    </div>
  );
}
```

## Authentication Flow

### 1. Sign In Process
1. User clicks "Sign In" button
2. `redirectToSignIn()` redirects to AWS Cognito Hosted UI
3. User authenticates with Cognito
4. Cognito redirects to `/auth/callback` with authorization code
5. Callback page processes the authentication
6. Tokens are stored securely
7. User is redirected to intended page

### 2. Token Management
- **Access Token**: Used for API requests
- **ID Token**: Contains user information
- **Refresh Token**: Managed internally by Amplify
- **Automatic Refresh**: Tokens refreshed before expiration

### 3. Route Protection
- **Public Routes**: Home, sign in, sign up, blog, etc.
- **Protected Routes**: Profile, settings, dashboard
- **Client-side Check**: No server-side middleware required

## API Integration

### Making Authenticated Requests

```tsx
import { useAuth } from '@/hooks/useAuth';

function ApiComponent() {
  const { getAccessToken } = useAuth();

  const fetchData = async () => {
    const token = await getAccessToken();
    
    const response = await fetch('/api/data', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });
    
    return response.json();
  };

  // ... rest of component
}
```

## Error Handling

The system includes comprehensive error handling:

- **Network Errors**: Graceful handling of connection issues
- **Token Expiration**: Automatic refresh or re-authentication
- **Configuration Errors**: Clear error messages for setup issues
- **User Feedback**: Error states displayed in UI

## Security Considerations

### ✅ Implemented
- **HTTPS Only**: All authentication flows use HTTPS
- **Token Validation**: JWT tokens validated before use
- **Secure Storage**: Tokens stored in localStorage with error handling
- **CSRF Protection**: OAuth state parameter validation
- **Domain Validation**: Redirect URLs validated by Cognito

### 🔄 Recommendations
- **Token Encryption**: Consider encrypting tokens in localStorage
- **CSP Headers**: Implement Content Security Policy
- **Rate Limiting**: Add rate limiting for authentication attempts

## Deployment

### Static Export Build

```bash
npm run build
```

This creates a static export in the `out/` directory that can be deployed to:
- **AWS S3 + CloudFront**
- **Vercel**
- **Netlify**
- **Any static hosting service**

### AWS S3 Deployment

1. Build the static export: `npm run build`
2. Upload `out/` directory to S3 bucket
3. Configure S3 for static website hosting
4. Set up CloudFront distribution
5. Update Cognito redirect URLs to match your domain

## Troubleshooting

### Common Issues

1. **"useSearchParams should be wrapped in suspense"**
   - ✅ Fixed: All pages using `useSearchParams` are wrapped in `Suspense`

2. **"Module not found: aws-config"**
   - ✅ Fixed: Old aws-config removed, new auth system implemented

3. **"Export useAuth doesn't exist"**
   - ✅ Fixed: Proper exports from AuthContext

4. **Build fails with JSX errors**
   - ✅ Fixed: All JSX properly handled with React imports

### Debug Mode

Enable debug logging by setting:
```bash
NEXT_PUBLIC_DEBUG_AUTH=true
```

## Migration Notes

### From Previous System
- ✅ Removed `middleware.ts` (incompatible with static export)
- ✅ Removed API routes in `/api/auth/`
- ✅ Replaced server-side authentication with client-side
- ✅ Updated all components to use new auth system

### Breaking Changes
- `logout()` → `signOut()`
- `redirectToLogin()` → `redirectToSignIn()`
- Server-side route protection → Client-side route protection

## Support

For issues or questions:
1. Check this documentation
2. Review the TypeScript types for API reference
3. Check browser console for error messages
4. Verify environment variables are set correctly
