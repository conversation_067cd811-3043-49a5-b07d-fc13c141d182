const fs = require('fs');
const path = require('path');

// Paths
const researchDocDir = path.join(__dirname, '../../research_doc');
const publicResearchDir = path.join(__dirname, '../public/research');

// Ensure public/research directory exists
if (!fs.existsSync(publicResearchDir)) {
  fs.mkdirSync(publicResearchDir, { recursive: true });
}

// Function to copy directory recursively
function copyDir(src, dest) {
  if (!fs.existsSync(src)) {
    console.log(`Source directory ${src} does not exist`);
    return;
  }

  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true });
  }

  const entries = fs.readdirSync(src, { withFileTypes: true });

  for (const entry of entries) {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);

    if (entry.isDirectory()) {
      copyDir(srcPath, destPath);
    } else {
      fs.copyFileSync(srcPath, destPath);
      console.log(`Copied: ${srcPath} -> ${destPath}`);
    }
  }
}

// Copy all research assets
try {
  console.log('Copying research assets...');
  copyDir(researchDocDir, publicResearchDir);
  console.log('Research assets copied successfully!');
} catch (error) {
  console.error('Error copying research assets:', error);
  process.exit(1);
}
