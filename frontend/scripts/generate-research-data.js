const fs = require('fs');
const path = require('path');
const matter = require('gray-matter');

// Paths
const researchDir = path.join(__dirname, '../public/research');
const outputFile = path.join(__dirname, '../src/lib/content/research-data.ts');

function generateSlug(folder) {
  return folder.toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim();
}

function parseMarkdownFile(content, folder) {
  try {
    const { data, content: markdownContent } = matter(content);

    // Extract title from first heading if not in frontmatter
    const titleMatch = markdownContent.match(/^#\s+(.+)$/m);
    const title = data.title || (titleMatch ? titleMatch[1] : folder);

    // Generate slug from folder name
    const slug = generateSlug(folder);

    // Extract excerpt from content (first meaningful paragraph)
    const excerptMatch = markdownContent
      .replace(/^#{1,6}\s+.+$/gm, '') // Remove headers
      .replace(/^\s*$/gm, '') // Remove empty lines
      .match(/^(.+?)(?:\n\n|\n#{1,6}|$)/m);

    let excerpt = data.excerpt;
    if (!excerpt && excerptMatch) {
      excerpt = excerptMatch[1].substring(0, 200);
      if (excerptMatch[1].length > 200) {
        excerpt += '...';
      }
    }
    if (!excerpt) {
      excerpt = 'Advanced threat intelligence research and analysis.';
    }

    // Process image paths in markdown content
    const processedContent = markdownContent.replace(
      /!\[([^\]]*)\]\(([^)]+)\)/g,
      (match, altText, imagePath) => {
        // If it's a relative path, convert it to the research assets path
        if (!imagePath.startsWith('http') && !imagePath.startsWith('/')) {
          const newPath = `/research/${folder}/${imagePath}`;
          return `![${altText}](${newPath})`;
        }
        return match;
      }
    );

    // Calculate read time (rough estimate: 200 words per minute)
    const wordCount = processedContent.split(/\s+/).length;
    const readTime = Math.ceil(wordCount / 200);

    // Extract IOCs if present
    const iocSection = markdownContent.match(/##?\s*IOC[s]?\s*\n([\s\S]*?)(?=\n##|\n#|$)/i);
    const iocs = [];
    if (iocSection) {
      const iocText = iocSection[1];
      // Extract hash-like patterns (MD5, SHA1, SHA256)
      const hashPattern = /\b[a-fA-F0-9]{32,64}\b/g;
      const matches = iocText.match(hashPattern);
      if (matches) {
        iocs.push(...matches);
      }
    }

    return {
      slug,
      title,
      excerpt,
      content: processedContent,
      date: data.date || new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }),
      author: data.author || 'Knownsec 404 Team',
      authorRole: data.authorRole || 'Advanced Threat Intelligence Team',
      image: data.image || '/img/home/<USER>',
      readTime: data.readTime || `${readTime} min read`,
      category: data.category || 'Threat Intelligence',
      tags: data.tags || ['Threat Intelligence', 'Security Research'],
      iocs: iocs.length > 0 ? iocs : undefined
    };
  } catch (error) {
    console.error(`Error parsing markdown file for ${folder}:`, error);
    return null;
  }
}

function generateResearchData() {
  const articles = [];

  try {
    if (!fs.existsSync(researchDir)) {
      console.log('Research directory not found, creating empty data file');
      const tsContent = `// Auto-generated research data
export const researchData = [];
`;
      fs.writeFileSync(outputFile, tsContent);
      return;
    }

    const folders = fs.readdirSync(researchDir);

    for (const folder of folders) {
      const folderPath = path.join(researchDir, folder);
      if (fs.statSync(folderPath).isDirectory()) {
        // Look for English markdown files
        const files = fs.readdirSync(folderPath);
        const enFile = files.find(file => file.endsWith('_en.md'));

        if (enFile) {
          const filePath = path.join(folderPath, enFile);
          const fileContent = fs.readFileSync(filePath, 'utf-8');

          // Parse frontmatter and content
          const article = parseMarkdownFile(fileContent, folder);
          if (article) {
            articles.push(article);
            console.log(`Processed: ${article.title}`);
          }
        }
      }
    }

    // Sort articles by date (newest first)
    articles.sort((a, b) => new Date(b.date) - new Date(a.date));

    // Write to TypeScript file
    const tsContent = `// Auto-generated research data
export const researchData = ${JSON.stringify(articles, null, 2)};
`;
    fs.writeFileSync(outputFile, tsContent);
    console.log(`Generated research data with ${articles.length} articles`);

  } catch (error) {
    console.error('Error generating research data:', error);
    // Create empty file on error
    const tsContent = `// Auto-generated research data
export const researchData = [];
`;
    fs.writeFileSync(outputFile, tsContent);
  }
}

// Run the script
generateResearchData();
