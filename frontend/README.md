# Gcandle Intelligence SaaS Frontend

This is a comprehensive Next.js-based SaaS frontend application for Gcandle Intelligence, a threat intelligence platform. The application provides a modern, responsive interface with complete subscription management, pricing plans, and payment processing capabilities.

## 🚀 Features

### Core SaaS Features
- **Comprehensive Pricing Page**: Multi-tier plans with feature comparison tables inspired by Criminal IP
- **Subscription Management**: Full billing lifecycle with Stripe integration
- **Payment Processing**: Secure checkout, customer portal, and billing management
- **User Authentication**: AWS Cognito with hosted UI and JWT token management
- **Feature Access Control**: Role-based access based on subscription tier
- **Usage Tracking**: Monitor API calls and search limits per plan

### Business Features
- **Multi-tier Plans**: Community (Free), Professional ($99/month), Enterprise (Custom)
- **Billing Cycles**: Monthly and annual billing with 17% annual discount
- **Customer Portal**: Self-service billing management via Stripe
- **Real-time Updates**: Live subscription status and usage monitoring
- **Email Notifications**: Automated billing and subscription emails

### Technical Features
- **Static Export**: Optimized for AWS S3/CloudFront deployment
- **Threat Intelligence Demo**: Interactive platform demonstration
- **Research Section**: Markdown-based threat research articles
- **SEO Optimized**: Built-in SEO with proper meta tags and sitemap
- **Error Handling**: Comprehensive error handling and user feedback
- **Mobile Responsive**: Fully responsive design for all devices

## 🛠 Tech Stack

- **Framework**: Next.js 15 with App Router and static export
- **Styling**: Tailwind CSS with custom design system
- **UI Components**: Radix UI primitives with shadcn/ui
- **Authentication**: AWS Cognito with Amplify v6
- **Payments**: Stripe with React integration
- **State Management**: React Context and custom hooks
- **Icons**: Lucide React
- **Deployment**: AWS S3 + CloudFront

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── about/             # About page
│   ├── auth/              # Authentication pages & callbacks
│   ├── blog/              # Blog section
│   ├── contactus/         # Contact page
│   ├── demo/              # Interactive threat intel demo
│   ├── plan/              # Comprehensive pricing page ⭐
│   ├── privacy/           # Privacy policy
│   ├── profile/           # User dashboard with subscription management ⭐
│   ├── research/          # Research articles (Markdown-based)
│   ├── resources/         # Resources section
│   ├── signin/            # Sign in page
│   ├── signup/            # Sign up page
│   ├── terms/             # Terms of service
│   └── whitepaper/        # Whitepapers
├── components/            # Reusable components
│   ├── ui/               # Base UI components (shadcn/ui)
│   ├── pricing/          # Pricing-specific components ⭐
│   ├── subscription/     # Subscription management components ⭐
│   ├── research/         # Research-specific components
│   ├── header.tsx        # Site header with auth integration
│   └── footer.tsx        # Site footer
├── contexts/             # React contexts
│   └── AuthContext.tsx   # Global authentication state
├── hooks/                # Custom React hooks
│   ├── useAuth.ts        # Authentication hook
│   ├── useSubscription.ts # Subscription management hook ⭐
│   └── useRouteProtection.ts # Client-side route protection
├── lib/                  # Utility libraries
│   ├── auth/             # Authentication utilities & token management
│   ├── stripe/           # Stripe integration & payment processing ⭐
│   ├── content/          # Content management utilities
│   └── utils.ts          # General utilities
└── public/               # Static assets
    ├── img/              # Images and logos
    └── research/         # Research content (Markdown files)
```

⭐ = New SaaS-specific components and features

## 🚀 Getting Started

### Prerequisites

- Node.js 18+ and npm/yarn
- AWS Account with Cognito setup
- Stripe account for payment processing
- Domain name for production deployment

### Local Development

1. **Clone the repository**:
   ```bash
   git clone <your-repo-url>
   cd aws-gcandle/frontend
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your actual values
   ```

4. **Run the development server**:
   ```bash
   npm run dev
   ```

5. **Open your browser**:
   Visit [http://localhost:9001](http://localhost:9001)

### Environment Configuration

Create a `.env.local` file with the following variables:

```bash
# AWS Cognito Configuration
NEXT_PUBLIC_COGNITO_USER_POOL_ID=us-east-1_xxxxxxxxx
NEXT_PUBLIC_COGNITO_CLIENT_ID=xxxxxxxxxxxxxxxxxxxxxxxxxx
NEXT_PUBLIC_COGNITO_DOMAIN=auth.yourdomain.com
NEXT_PUBLIC_API_ENDPOINT=https://api.yourdomain.com

# Stripe Configuration
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_xxxxxxxxxxxxxxxxxxxxx
NEXT_PUBLIC_STRIPE_PROFESSIONAL_MONTHLY_PRICE_ID=price_xxxxxxxxxxxxx
NEXT_PUBLIC_STRIPE_PROFESSIONAL_ANNUAL_PRICE_ID=price_xxxxxxxxxxxxx

# Redirect URLs
NEXT_PUBLIC_REDIRECT_SIGN_IN=http://localhost:9001/auth/callback
NEXT_PUBLIC_REDIRECT_SIGN_OUT=http://localhost:9001
```

See `.env.example` for complete configuration options.

## 🎨 Design System

The application features a cohesive design language inspired by Criminal IP:

- **Color Scheme**:
  - Primary: Dark blue (#202438)
  - Secondary: Red (#d82a21)
  - Accent: Green (#4BB175)
- **Typography**: Mona Sans font family for modern readability
- **Components**: Rounded corners, subtle shadows, smooth transitions
- **Layout**: Responsive grid system with mobile-first approach
- **Accessibility**: WCAG 2.1 compliant with proper contrast ratios

## 💳 Pricing Plans

### Community (Free)
- 10 intelligence searches per day
- Basic threat intelligence data
- Community API access
- Email support

### Professional ($99/month, $990/year)
- 1,000 intelligence searches per day
- Advanced threat intelligence data
- Full API access
- Priority support
- Advanced analytics
- Custom dashboards

### Enterprise (Custom pricing)
- Unlimited intelligence searches
- Premium threat intelligence data
- Full API + premium endpoints
- 24/7 phone support
- Custom integrations
- Dedicated account manager

## 🔐 Authentication Flow

1. User clicks "Sign In" → Redirects to AWS Cognito Hosted UI
2. User authenticates with Cognito
3. Cognito redirects to `/auth/callback` with authorization code
4. Frontend processes authentication and stores JWT tokens
5. User gains access to protected features based on subscription tier

## 💰 Payment Flow

1. User selects a plan on `/plan` page
2. Stripe checkout session is created
3. User completes payment on Stripe
4. Webhook updates subscription status in backend
5. User gains access to premium features
6. Billing management available via Stripe customer portal

## 环境配置

项目支持不同的环境配置：

### 环境变量文件

- `.env.development` - 开发环境配置
- `.env.production` - 生产环境配置
- `.env.local` - 本地覆盖配置（不提交到版本控制）

### 环境变量

主要环境变量包括：

```
# 环境标识
NEXT_PUBLIC_ENV=development|production

# AWS Cognito配置
NEXT_PUBLIC_COGNITO_USER_POOL_ID=your-user-pool-id
NEXT_PUBLIC_COGNITO_CLIENT_ID=your-client-id
NEXT_PUBLIC_COGNITO_DOMAIN=your-domain.auth.region.amazoncognito.com

# 重定向URL
NEXT_PUBLIC_REDIRECT_SIGN_IN=comma,separated,urls
NEXT_PUBLIC_REDIRECT_SIGN_OUT=comma,separated,urls

# API配置
NEXT_PUBLIC_API_ENDPOINT=your-api-endpoint

# S3配置
NEXT_PUBLIC_S3_BUCKET=your-bucket-name
NEXT_PUBLIC_S3_REGION=your-region
```

## 认证流程

项目使用AWS Cognito进行用户认证，流程如下：

1. 用户点击登录/注册按钮
2. 使用`signInWithRedirect`方法重定向到Cognito托管UI
3. 用户在Cognito托管UI完成认证
4. Cognito将用户重定向回应用的回调URL
5. 回调处理程序验证认证并获取用户会话

### 认证回调处理

认证回调有两种处理方式：

1. 前端页面处理：`/auth/callback/page.tsx`
2. API路由处理：`/api/auth/callback/route.ts`

## 注意事项

- Cognito域名格式应为`your-domain.auth.region.amazoncognito.com`，不包含协议前缀
- 确保Cognito应用客户端配置中的回调URL与应用中配置的一致
- 本地开发时，可以创建`.env.local`文件覆盖默认配置

## 未来增强

以下是一些潜在的增强功能：
- 添加威胁情报平台的实时交互演示
- 为白皮书和报告等受限内容实现用户认证
- 创建常见安全术语和概念的可搜索知识库
- 添加展示与真实组织成功实施的案例研究
- 开发API游乐场，让用户在注册前测试API功能
