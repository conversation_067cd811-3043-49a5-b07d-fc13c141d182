/** @type {import('next').NextConfig} */

// 获取当前环境
const isDevelopment = process.env.NODE_ENV === 'development';

// 加载环境变量
require('dotenv').config({
  path: isDevelopment 
    ? '.env.development' 
    : '.env.production'
});

// 如果存在.env.local文件，它会自动覆盖上面的配置

const nextConfig = {
  // Enable static export for S3 deployment
  output: 'export',
  trailingSlash: true,

  // Environment variables
  env: {
    NEXT_PUBLIC_ENV: process.env.NEXT_PUBLIC_ENV || (isDevelopment ? 'development' : 'production'),
  },

  // Image optimization for static export
  images: {
    unoptimized: true,
    domains: [
      "source.unsplash.com",
      "images.unsplash.com",
      "ext.same-assets.com",
      "ugc.same-assets.com",
    ],
    remotePatterns: [
      {
        protocol: "https",
        hostname: "source.unsplash.com",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "images.unsplash.com",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "ext.same-assets.com",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "ugc.same-assets.com",
        pathname: "/**",
      },
    ],
  },

  // TypeScript and ESLint configuration
  typescript: {
    ignoreBuildErrors: false, // Enable type checking for better code quality
  },
  eslint: {
    ignoreDuringBuilds: false, // Enable linting for better code quality
  },

  // Environment-specific configuration
  ...(isDevelopment ? {
    // Development environment
  } : {
    // Production environment
    poweredByHeader: false,
    generateEtags: false, // Disable for static export
  }),
};

module.exports = nextConfig;
