## 发布新的研究文章步骤
1. 准备Markdown文件
   在research_doc目录中创建新的研究文章文件夹
   确保英文版本的Markdown文件使用_en.md后缀
   添加相关的图片和资源到该文件夹中

2. 构建项目
   cd frontend
   npm run build
   这个命令会执行两个关键脚本：
   scripts/generate-research-data.js：生成研究文章数据
   scripts/copy-research-assets.js：将研究资源复制到public/research目录

3.部署到AWS S3
  将生成的out/目录内容上传到您的S3存储桶
  确保S3配置为静态网站托管
  如果使用CloudFront，可能需要创建缓存失效
  Markdown文件格式要求
  您的Markdown文件应包含以下frontmatter信息：
  ------------------------------------------------------------
  ---
  title: "您的研究文章标题"
  date: "2023-12-01"
  author: "作者名称"
  authorRole: "职位"
  image: "/path/to/cover-image.jpg"
  category: "威胁情报"
  tags: ["威胁情报", "安全研究", "恶意软件"]
  ---

  # 您的研究文章标题

  文章内容...
  ------------------------------------------------------------

  PS：如果之前已经提交过out目录到S3存储桶，那么可以在本地新添文章到research目录时，
      执行npm run generate-research
      这个命令会生成文章数据到research-data.ts，
      那么部署到AWS时，只需要替换research-data.ts和上传research中新添的文章即可。
4. 自动化处理
  系统会自动：

  从Markdown文件中提取元数据
  生成文章摘要
  处理图片路径，确保它们在静态导出中正确显示
  提取IOC（威胁指标）
  生成适当的slug用于URL