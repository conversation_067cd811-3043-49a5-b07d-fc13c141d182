# SaaS Frontend Deployment Guide

## Prerequisites

Before starting the deployment, ensure you have:

1. **AWS Account** with appropriate permissions
2. **Domain name** registered (e.g., gcandle.io)
3. **Stripe account** for payment processing
4. **Node.js 18+** and **npm/yarn** installed
5. **AWS CLI** configured
6. **Git** for version control

## Step-by-Step Deployment

### Step 1: Environment Setup

1. **Clone the repository**:
   ```bash
   git clone <your-repo-url>
   cd aws-gcandle/frontend
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Create environment files**:
   ```bash
   # Create production environment file
   cp .env.example .env.production
   
   # Create local environment file for testing
   cp .env.example .env.local
   ```

4. **Configure environment variables** in `.env.production`:
   ```bash
   # AWS Configuration
   NEXT_PUBLIC_AWS_REGION=us-east-1
   NEXT_PUBLIC_COGNITO_USER_POOL_ID=us-east-1_xxxxxxxxx
   NEXT_PUBLIC_COGNITO_CLIENT_ID=xxxxxxxxxxxxxxxxxxxxxxxxxx
   NEXT_PUBLIC_COGNITO_DOMAIN=auth.gcandle.io
   NEXT_PUBLIC_API_ENDPOINT=https://api.gcandle.io
   
   # Redirect URLs
   NEXT_PUBLIC_REDIRECT_SIGN_IN=https://gcandle.io/auth/callback
   NEXT_PUBLIC_REDIRECT_SIGN_OUT=https://gcandle.io
   
   # Stripe Configuration
   NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_xxxxxxxxxxxxxxxxxxxxx
   NEXT_PUBLIC_STRIPE_PROFESSIONAL_MONTHLY_PRICE_ID=price_xxxxxxxxxxxxx
   NEXT_PUBLIC_STRIPE_PROFESSIONAL_ANNUAL_PRICE_ID=price_xxxxxxxxxxxxx
   ```

### Step 2: AWS Infrastructure Setup

1. **Create S3 bucket for hosting**:
   ```bash
   aws s3 mb s3://gcandle-frontend-prod --region us-east-1
   ```

2. **Configure S3 bucket for static website hosting**:
   ```bash
   aws s3 website s3://gcandle-frontend-prod \
     --index-document index.html \
     --error-document 404.html
   ```

3. **Set bucket policy for public read access**:
   ```bash
   cat > bucket-policy.json << EOF
   {
     "Version": "2012-10-17",
     "Statement": [
       {
         "Sid": "PublicReadGetObject",
         "Effect": "Allow",
         "Principal": "*",
         "Action": "s3:GetObject",
         "Resource": "arn:aws:s3:::gcandle-frontend-prod/*"
       }
     ]
   }
   EOF
   
   aws s3api put-bucket-policy \
     --bucket gcandle-frontend-prod \
     --policy file://bucket-policy.json
   ```

4. **Create CloudFront distribution**:
   ```bash
   cat > cloudfront-config.json << EOF
   {
     "CallerReference": "gcandle-frontend-$(date +%s)",
     "Comment": "Gcandle Frontend Distribution",
     "DefaultRootObject": "index.html",
     "Origins": {
       "Quantity": 1,
       "Items": [
         {
           "Id": "S3-gcandle-frontend-prod",
           "DomainName": "gcandle-frontend-prod.s3.amazonaws.com",
           "S3OriginConfig": {
             "OriginAccessIdentity": ""
           }
         }
       ]
     },
     "DefaultCacheBehavior": {
       "TargetOriginId": "S3-gcandle-frontend-prod",
       "ViewerProtocolPolicy": "redirect-to-https",
       "TrustedSigners": {
         "Enabled": false,
         "Quantity": 0
       },
       "ForwardedValues": {
         "QueryString": false,
         "Cookies": {
           "Forward": "none"
         }
       },
       "MinTTL": 0
     },
     "Enabled": true,
     "PriceClass": "PriceClass_100"
   }
   EOF
   
   aws cloudfront create-distribution \
     --distribution-config file://cloudfront-config.json
   ```

### Step 3: Cognito Setup

1. **Create Cognito User Pool** (see AWS_INFRASTRUCTURE.md for detailed commands)

2. **Configure Hosted UI**:
   - Go to AWS Cognito Console
   - Select your User Pool
   - Go to "App integration" → "Domain"
   - Add custom domain: `auth.gcandle.io`
   - Configure OAuth settings

### Step 4: Stripe Setup

1. **Create Stripe account** and get API keys
   https://dashboard.stripe.com/

2. **Create products and prices**:
   ```bash
   # Professional Monthly
   stripe products create --name=prod_gcandle --description="Advanced threat intelligence features"
   stripe prices create --product=prod_gcandle --unit-amount=9900 --currency=usd --recurring[interval]=month
   
   # Professional Annual
   stripe prices create --product=prod_gcandle --unit-amount=99000 --currency=usd --recurring[interval]=year
   ```

3. **Set up webhooks**:
   Click Developers -> webhooks

   - Endpoint URL: `https://api.gcandle.io/stripe/webhook`
   - Events: `customer.subscription.*`, `invoice.payment_*`

### Step 5: Backend API Setup

1. **Create Lambda functions** for API endpoints:
   ```bash
   # Create deployment package
   zip -r api-functions.zip lambda/
   
   # Deploy Lambda functions
   aws lambda create-function \
     --function-name gcandle-api-subscription \
     --runtime nodejs18.x \
     --role arn:aws:iam::account:role/lambda-execution-role \
     --handler index.handler \
     --zip-file fileb://api-functions.zip
   ```

2. **Create API Gateway**:
   ```bash
   aws apigateway create-rest-api --name gcandle-api
   ```

3. **Set up DynamoDB tables**:
   ```bash
   # Users table
   aws dynamodb create-table \
     --table-name gcandle-users-prod \
     --attribute-definitions AttributeName=userId,AttributeType=S \
     --key-schema AttributeName=userId,KeyType=HASH \
     --billing-mode PAY_PER_REQUEST
   
   # Subscriptions table
   aws dynamodb create-table \
     --table-name gcandle-subscriptions-prod \
     --attribute-definitions AttributeName=userId,AttributeType=S AttributeName=subscriptionId,AttributeType=S \
     --key-schema AttributeName=userId,KeyType=HASH AttributeName=subscriptionId,KeyType=RANGE \
     --billing-mode PAY_PER_REQUEST
   ```

### Step 6: Build and Deploy Frontend

1. **Build the application**:
   ```bash
   npm run build
   ```

2. **Deploy to S3**:
   ```bash
   aws s3 sync out/ s3://gcandle-frontend-prod --delete
   ```

3. **Invalidate CloudFront cache**:
   ```bash
   aws cloudfront create-invalidation \
     --distribution-id E1234567890123 \
     --paths "/*"
   ```

### Step 7: DNS Configuration

1. **Create Route 53 records**:
   ```bash
   # Main domain
   aws route53 change-resource-record-sets \
     --hosted-zone-id Z1234567890123 \
     --change-batch file://dns-changes.json
   ```

2. **DNS changes file** (`dns-changes.json`):
   ```json
   {
     "Changes": [
       {
         "Action": "CREATE",
         "ResourceRecordSet": {
           "Name": "gcandle.io",
           "Type": "A",
           "AliasTarget": {
             "DNSName": "d1234567890123.cloudfront.net",
             "EvaluateTargetHealth": false,
             "HostedZoneId": "Z2FDTNDATAQYW2"
           }
         }
       }
     ]
   }
   ```

### Step 8: Testing

1. **Test authentication flow**:
   - Visit `https://gcandle.io`
   - Click "Sign In" → Should redirect to Cognito
   - Complete registration/login
   - Verify redirect back to app

2. **Test pricing page**:
   - Visit `https://gcandle.io/plan`
   - Verify all plans display correctly
   - Test plan selection buttons

3. **Test payment flow** (use Stripe test mode):
   - Select Professional plan
   - Complete checkout flow
   - Verify subscription creation

### Step 9: Monitoring Setup

1. **CloudWatch dashboards**:
   ```bash
   aws cloudwatch put-dashboard \
     --dashboard-name "Gcandle-Frontend" \
     --dashboard-body file://dashboard-config.json
   ```

2. **Set up alerts**:
   ```bash
   aws cloudwatch put-metric-alarm \
     --alarm-name "High-Error-Rate" \
     --alarm-description "Alert when error rate is high" \
     --metric-name "4xxErrorRate" \
     --namespace "AWS/CloudFront" \
     --statistic "Average" \
     --period 300 \
     --threshold 5.0 \
     --comparison-operator "GreaterThanThreshold"
   ```

## Post-Deployment Checklist

- [ ] SSL certificate is active and valid
- [ ] All environment variables are correctly set
- [ ] Authentication flow works end-to-end
- [ ] Payment processing is functional
- [ ] Email notifications are working
- [ ] Monitoring and alerts are configured
- [ ] Backup and disaster recovery plans are in place
- [ ] Security scanning is completed
- [ ] Performance testing is done
- [ ] Documentation is updated

## Troubleshooting

### Common Issues

1. **CORS Errors**:
   - Check API Gateway CORS configuration
   - Verify allowed origins include your domain

2. **Authentication Failures**:
   - Verify Cognito redirect URLs
   - Check JWT token expiration
   - Validate environment variables

3. **Payment Issues**:
   - Verify Stripe webhook endpoints
   - Check API key configuration
   - Test with Stripe test cards

4. **Build Failures**:
   - Check Node.js version compatibility
   - Verify all dependencies are installed
   - Review build logs for specific errors

### Support Resources

- **AWS Documentation**: https://docs.aws.amazon.com/
- **Stripe Documentation**: https://stripe.com/docs
- **Next.js Documentation**: https://nextjs.org/docs
- **Project Repository**: [Your GitHub repo URL]

## Maintenance

### Regular Tasks

1. **Security Updates**:
   - Update dependencies monthly
   - Review AWS security recommendations
   - Rotate API keys quarterly

2. **Performance Monitoring**:
   - Review CloudWatch metrics weekly
   - Optimize based on usage patterns
   - Monitor costs and usage

3. **Backup Verification**:
   - Test backup restoration monthly
   - Verify data integrity
   - Update disaster recovery procedures

This deployment guide provides a comprehensive approach to setting up your SaaS frontend with all necessary AWS services and integrations.
