# AWS Infrastructure for SaaS Frontend

## Overview

This document outlines the complete AWS infrastructure setup for hosting the Gcandle TI SaaS frontend with pricing, billing, and subscription management capabilities.

## Architecture Diagram

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   CloudFront    │────│       S3         │    │   Lambda        │
│   (CDN)         │    │   (Static Site)  │    │   (API)         │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                        │                       │
         │                        │                       │
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Route 53      │    │   API Gateway    │    │   DynamoDB      │
│   (DNS)         │    │   (REST API)     │    │   (Database)    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                        │                       │
         │                        │                       │
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   AWS Cognito   │    │     Stripe       │    │      SES        │
│   (Auth)        │    │   (Payments)     │    │   (Email)       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## Core Services

### 1. Frontend Hosting

**Amazon S3 + CloudFront**
- **S3 Bucket**: Static website hosting for Next.js export
- **CloudFront**: Global CDN for fast content delivery
- **Benefits**: Cost-effective, scalable, global distribution

**Configuration:**
```bash
# S3 Bucket Configuration
- Bucket Name: gcandle-frontend-prod
- Static Website Hosting: Enabled
- Index Document: index.html
- Error Document: 404.html
- Public Read Access: Enabled (via bucket policy)
```

**CloudFront Configuration:**
```bash
# CloudFront Distribution
- Origin: S3 bucket
- Default Root Object: index.html
- Error Pages: Custom 404 handling for SPA routing
- SSL Certificate: AWS Certificate Manager
- Compression: Enabled
- Caching: Optimized for static content
```

### 2. Authentication

**AWS Cognito**
- **User Pool**: User registration and authentication
- **Hosted UI**: Pre-built authentication pages
- **OAuth 2.0**: Social login integration
- **JWT Tokens**: Secure API access

**Configuration:**
```bash
# Cognito User Pool Settings
- Pool Name: gcandle-users-prod
- Username Attributes: Email
- Password Policy: Strong (8+ chars, mixed case, numbers, symbols)
- MFA: Optional (SMS/TOTP)
- Email Verification: Required
- Hosted UI Domain: auth.gcandle.io
```

### 3. API Layer

**API Gateway + Lambda**
- **API Gateway**: RESTful API endpoints
- **Lambda Functions**: Serverless business logic
- **Authorization**: Cognito JWT validation
- **CORS**: Configured for frontend domain

**API Endpoints:**
```bash
# Subscription Management
POST /stripe/create-checkout-session
POST /stripe/create-portal-session
GET  /stripe/subscription
POST /stripe/subscription/{id}/cancel

# User Management
GET  /user/profile
PUT  /user/profile
GET  /user/usage
POST /user/usage/reset

# Billing & Invoicing
GET  /billing/invoices
GET  /billing/usage
POST /billing/update-payment-method
```

### 4. Database

**Amazon DynamoDB**
- **Users Table**: User profiles and metadata
- **Subscriptions Table**: Subscription status and history
- **Usage Table**: API usage tracking
- **Billing Table**: Invoice and payment records

**Table Schemas:**
```bash
# Users Table
- Partition Key: userId (String)
- Attributes: email, name, createdAt, stripeCustomerId, plan

# Subscriptions Table
- Partition Key: userId (String)
- Sort Key: subscriptionId (String)
- Attributes: status, planId, currentPeriodStart, currentPeriodEnd

# Usage Table
- Partition Key: userId (String)
- Sort Key: date (String, YYYY-MM-DD)
- Attributes: searches, apiCalls, lastUpdated
```

### 5. Payment Processing

**Stripe Integration**
- **Checkout Sessions**: Secure payment processing
- **Customer Portal**: Self-service billing management
- **Webhooks**: Real-time subscription updates
- **Products & Prices**: Plan configuration

**Stripe Configuration:**
```bash
# Products
- Community: Free tier (no Stripe product needed)
- Professional: $99/month, $990/year
- Enterprise: Custom pricing

# Webhook Events
- customer.subscription.created
- customer.subscription.updated
- customer.subscription.deleted
- invoice.payment_succeeded
- invoice.payment_failed
```

### 6. Email Service

**Amazon SES**
- **Transactional Emails**: Welcome, billing notifications
- **Templates**: Pre-designed email templates
- **Bounce Handling**: Automatic bounce management
- **Analytics**: Email delivery metrics

## Environment Variables

Create the following environment variables for your application:

```bash
# AWS Configuration
NEXT_PUBLIC_AWS_REGION=us-east-1
NEXT_PUBLIC_COGNITO_USER_POOL_ID=us-east-1_xxxxxxxxx
NEXT_PUBLIC_COGNITO_CLIENT_ID=xxxxxxxxxxxxxxxxxxxxxxxxxx
NEXT_PUBLIC_COGNITO_DOMAIN=auth.gcandle.io
NEXT_PUBLIC_API_ENDPOINT=https://api.gcandle.io

# Redirect URLs
NEXT_PUBLIC_REDIRECT_SIGN_IN=https://gcandle.io/auth/callback
NEXT_PUBLIC_REDIRECT_SIGN_OUT=https://gcandle.io

# Stripe Configuration
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_xxxxxxxxxxxxxxxxxxxxx
NEXT_PUBLIC_STRIPE_PROFESSIONAL_MONTHLY_PRICE_ID=price_xxxxxxxxxxxxx
NEXT_PUBLIC_STRIPE_PROFESSIONAL_ANNUAL_PRICE_ID=price_xxxxxxxxxxxxx
NEXT_PUBLIC_STRIPE_ENTERPRISE_MONTHLY_PRICE_ID=price_xxxxxxxxxxxxx
NEXT_PUBLIC_STRIPE_ENTERPRISE_ANNUAL_PRICE_ID=price_xxxxxxxxxxxxx

# Backend Configuration (for Lambda functions)
STRIPE_SECRET_KEY=sk_live_xxxxxxxxxxxxxxxxxxxxx
STRIPE_WEBHOOK_SECRET=whsec_xxxxxxxxxxxxxxxxxxxxx
DYNAMODB_USERS_TABLE=gcandle-users-prod
DYNAMODB_SUBSCRIPTIONS_TABLE=gcandle-subscriptions-prod
DYNAMODB_USAGE_TABLE=gcandle-usage-prod
SES_FROM_EMAIL=<EMAIL>
```

## Security Considerations

### 1. Access Control
- **IAM Roles**: Least privilege principle
- **API Gateway**: Cognito authorizer
- **S3 Bucket**: Public read, no write access
- **DynamoDB**: Fine-grained access control

### 2. Data Protection
- **Encryption**: At rest and in transit
- **HTTPS**: Enforced via CloudFront
- **JWT Tokens**: Short expiration times
- **PCI Compliance**: Stripe handles card data

### 3. Monitoring & Logging
- **CloudWatch**: Application and infrastructure logs
- **X-Ray**: Distributed tracing
- **CloudTrail**: API audit logs
- **Stripe Dashboard**: Payment monitoring

## Cost Optimization

### 1. S3 + CloudFront
- **Estimated Cost**: $5-20/month for typical traffic
- **Optimization**: Intelligent tiering, compression

### 2. Lambda + API Gateway
- **Estimated Cost**: $10-50/month for typical usage
- **Optimization**: Function memory tuning, caching

### 3. DynamoDB
- **Estimated Cost**: $5-25/month for typical usage
- **Optimization**: On-demand billing, efficient queries

### 4. Cognito
- **Estimated Cost**: $0.0055 per MAU (first 50,000 free)
- **Optimization**: Efficient token refresh

**Total Estimated Monthly Cost: $50-200** (depending on traffic and usage)

## Scalability

### 1. Automatic Scaling
- **CloudFront**: Global edge locations
- **Lambda**: Automatic concurrency scaling
- **DynamoDB**: On-demand scaling
- **API Gateway**: Built-in scaling

### 2. Performance Optimization
- **CDN Caching**: Static assets cached globally
- **Lambda Cold Starts**: Provisioned concurrency for critical functions
- **Database**: Efficient indexing and query patterns
- **Frontend**: Code splitting and lazy loading

## Disaster Recovery

### 1. Backup Strategy
- **S3**: Cross-region replication
- **DynamoDB**: Point-in-time recovery
- **Lambda**: Version control and aliases
- **Cognito**: User pool backup

### 2. Multi-Region Setup
- **Primary Region**: us-east-1
- **Secondary Region**: us-west-2
- **Failover**: Route 53 health checks
- **Data Sync**: Cross-region replication

## Compliance

### 1. Data Privacy
- **GDPR**: User data deletion capabilities
- **CCPA**: Data export and deletion
- **SOC 2**: AWS compliance inheritance

### 2. Security Standards
- **PCI DSS**: Stripe compliance
- **ISO 27001**: AWS compliance
- **HIPAA**: Available if needed

## Next Steps

1. **Infrastructure Setup**: Deploy AWS resources
2. **Stripe Configuration**: Set up products and webhooks
3. **Domain Setup**: Configure custom domains
4. **SSL Certificates**: Set up HTTPS
5. **Monitoring**: Configure alerts and dashboards
6. **Testing**: End-to-end testing of payment flows
7. **Go Live**: Production deployment

This infrastructure provides a robust, scalable, and cost-effective foundation for your SaaS application with enterprise-grade security and compliance capabilities.

## Detailed Deployment Steps

### Phase 1: AWS Account Setup

1. **Create AWS Account** (if not already done)
2. **Set up IAM User** with appropriate permissions
3. **Configure AWS CLI**:
   ```bash
   aws configure
   # Enter Access Key ID, Secret Access Key, Region (us-east-1), Output format (json)
   ```

### Phase 2: Domain and SSL Setup

1. **Register Domain** (or use existing)
2. **Create Route 53 Hosted Zone**:
   ```bash
   aws route53 create-hosted-zone --name gcandle.io --caller-reference $(date +%s)
   ```
3. **Request SSL Certificate**:
   ```bash
   aws acm request-certificate \
     --domain-name gcandle.io \
     --subject-alternative-names "*.gcandle.io" \
     --validation-method DNS \
     --region us-east-1
   ```

### Phase 3: Cognito Setup

1. **Create User Pool**:
   ```bash
   aws cognito-idp create-user-pool \
     --pool-name gcandle-users-prod \
     --policies PasswordPolicy='{MinimumLength=8,RequireUppercase=true,RequireLowercase=true,RequireNumbers=true,RequireSymbols=true}' \
     --auto-verified-attributes email \
     --username-attributes email
   ```

2. **Create User Pool Client**:
   ```bash
   aws cognito-idp create-user-pool-client \
     --user-pool-id us-east-1_xxxxxxxxx \
     --client-name gcandle-frontend \
     --generate-secret false \
     --supported-identity-providers COGNITO \
     --callback-urls "https://gcandle.io/auth/callback" \
     --logout-urls "https://gcandle.io" \
     --allowed-o-auth-flows code \
     --allowed-o-auth-scopes openid email profile \
     --allowed-o-auth-flows-user-pool-client
   ```

3. **Create User Pool Domain**:
   ```bash
   aws cognito-idp create-user-pool-domain \
     --domain auth.gcandle.io \
     --user-pool-id us-east-1_xxxxxxxxx \
     --custom-domain-config CertificateArn=arn:aws:acm:us-east-1:account:certificate/cert-id
   ```
