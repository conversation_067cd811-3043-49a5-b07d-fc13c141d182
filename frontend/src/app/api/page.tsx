import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";

export default function ApiPage() {
  return (
    <div className="bg-white">
      {/* Header */}
      <section className="bg-[#1b2135] text-white py-20">
        <div className="max-w-[1200px] mx-auto px-4 text-center">
          <h1 className="text-5xl font-bold mb-6">Gcandle API</h1>
          <p className="text-xl mb-10">
            Access our high-fidelity threat intelligence through simple API calls
          </p>
          <Link href="/api/documentation">
            <Button className="bg-[#d82a21] hover:bg-[#c82218] rounded-full px-8 py-6">
              View API Documentation
            </Button>
          </Link>
        </div>
      </section>

      {/* API Information */}
      <section className="py-20">
        <div className="max-w-[1200px] mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center">Available APIs</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Community API */}
            <Card className="p-8 shadow-md hover:shadow-lg transition-shadow">
              <h3 className="text-2xl font-bold mb-4">Community API</h3>
              <p className="text-gray-600 mb-6">
                Free access to basic threat intelligence data with daily limits.
                Perfect for individual researchers and small security teams.
              </p>
              <div className="space-y-4 mb-8">
                <div>
                  <h4 className="font-medium">Available Endpoints:</h4>
                  <ul className="list-disc pl-5 text-gray-600 mt-2">
                    <li>IP Report</li>
                    <li>Domain Report</li>
                    <li>URL Report</li>
                    <li>Basic File Hash Report</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium">Rate Limits:</h4>
                  <p className="text-gray-600 mt-2">10 requests per day</p>
                </div>
              </div>
              <Link href="/api/ip-report">
                <Button className="bg-[#4BB175] hover:bg-[#3a9c5f] text-white w-full">
                  Get Free API Key
                </Button>
              </Link>
            </Card>

            {/* Premium API */}
            <Card className="p-8 shadow-md hover:shadow-lg transition-shadow">
              <h3 className="text-2xl font-bold mb-4">Premium API</h3>
              <p className="text-gray-600 mb-6">
                Enterprise-grade threat intelligence with advanced data and higher rate limits.
                Designed for organizations requiring actionable intelligence.
              </p>
              <div className="space-y-4 mb-8">
                <div>
                  <h4 className="font-medium">Available Endpoints:</h4>
                  <ul className="list-disc pl-5 text-gray-600 mt-2">
                    <li>All Community API endpoints</li>
                    <li>Compromise Detection</li>
                    <li>Advanced Threat Analysis</li>
                    <li>Behavior Pattern Recognition</li>
                    <li>Integrated Intelligence Feeds</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium">Rate Limits:</h4>
                  <p className="text-gray-600 mt-2">Customizable based on requirements</p>
                </div>
              </div>
              <Link href="/contactus">
                <Button className="bg-[#202438] hover:bg-[#171b2e] text-white w-full">
                  Contact for Pricing
                </Button>
              </Link>
            </Card>
          </div>
        </div>
      </section>

      {/* Example Usage */}
      <section className="py-20 bg-slate-50">
        <div className="max-w-[1200px] mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center">Example Usage</h2>

          <div className="bg-[#202438] rounded-lg p-4 md:p-8 text-white overflow-auto">
            <pre className="font-mono text-sm md:text-base">
{`# Python Example
import requests

API_KEY = "your_api_key"
BASE_URL = "https://api.threatbook.io/v1"

# Get IP report
def get_ip_report(ip_address):
    endpoint = f"{BASE_URL}/ip/report"
    params = {
        "apikey": API_KEY,
        "resource": ip_address
    }

    response = requests.get(endpoint, params=params)
    return response.json()

# Example usage
result = get_ip_report("***********")
print(result)`}
            </pre>
          </div>
        </div>
      </section>

      {/* CTA Banner */}
      <section className="py-16 bg-[#1b2135] text-white">
        <div className="max-w-[1200px] mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-8">Ready to integrate Gcandle Intelligence?</h2>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link href="/api/documentation">
              <Button className="bg-white text-[#1b2135] hover:bg-gray-100 rounded-full px-6">
                Read Documentation
              </Button>
            </Link>
            <Link href="/plan">
              <Button className="bg-[#d82a21] hover:bg-[#c82218] text-white rounded-full px-6">
                Choose a Plan
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
