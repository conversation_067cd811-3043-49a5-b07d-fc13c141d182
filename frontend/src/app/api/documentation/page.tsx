import Link from "next/link";
import { Button } from "@/components/ui/button";

export default function ApiDocumentationPage() {
  return (
    <div className="bg-white">
      {/* Header */}
      <section className="bg-[#1b2135] text-white py-20">
        <div className="max-w-[1200px] mx-auto px-4 text-center">
          <h1 className="text-5xl font-bold mb-6">API Documentation</h1>
          <p className="text-xl">
            Comprehensive guide to using Gcandle Intelligence APIs
          </p>
        </div>
      </section>

      {/* Documentation Content */}
      <section className="py-20">
        <div className="max-w-[1200px] mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-8">
            {/* Sidebar */}
            <div className="w-full md:w-1/4 bg-slate-50 p-6 rounded-lg">
              <h3 className="text-xl font-bold mb-4">Contents</h3>
              <ul className="space-y-4">
                <li>
                  <a href="#introduction" className="text-[#d82a21] hover:underline">
                    Introduction
                  </a>
                </li>
                <li>
                  <a href="#authentication" className="text-[#d82a21] hover:underline">
                    Authentication
                  </a>
                </li>
                <li>
                  <a href="#endpoints" className="text-[#d82a21] hover:underline">
                    API Endpoints
                  </a>
                </li>
                <li>
                  <a href="#rate-limits" className="text-[#d82a21] hover:underline">
                    Rate Limits
                  </a>
                </li>
                <li>
                  <a href="#examples" className="text-[#d82a21] hover:underline">
                    Examples
                  </a>
                </li>
                <li>
                  <a href="#faq" className="text-[#d82a21] hover:underline">
                    FAQ
                  </a>
                </li>
              </ul>
            </div>

            {/* Main Content */}
            <div className="w-full md:w-3/4">
              <section id="introduction" className="mb-12">
                <h2 className="text-3xl font-bold mb-4">Introduction</h2>
                <p className="text-gray-600 mb-4">
                  Gcandle provides a comprehensive set of APIs that allow you to access our high-fidelity
                  threat intelligence data programmatically. Our APIs are designed to be easy to use and
                  integrate with your existing security infrastructure.
                </p>
                <p className="text-gray-600">
                  Whether you're looking to enrich your security data with threat intelligence or
                  automate your threat detection and response processes, Gcandle APIs provide
                  the tools you need to make informed security decisions.
                </p>
              </section>

              <section id="authentication" className="mb-12">
                <h2 className="text-3xl font-bold mb-4">Authentication</h2>
                <p className="text-gray-600 mb-4">
                  All API requests require authentication using an API key. You can obtain an API key
                  by signing up for a free account or purchasing an enterprise subscription.
                </p>
                <div className="bg-slate-50 p-4 rounded-md my-6">
                  <h4 className="font-bold mb-2">Example Request with API Key</h4>
                  <pre className="bg-[#202438] text-white p-4 rounded-md overflow-auto">
{"https://api.threatbook.io/v1/ip/report?apikey=YOUR_API_KEY&resource=***********"}
                  </pre>
                </div>
              </section>

              <section id="endpoints" className="mb-12">
                <h2 className="text-3xl font-bold mb-4">API Endpoints</h2>
                <p className="text-gray-600 mb-6">
                  Gcandle offers the following API endpoints:
                </p>

                <div className="space-y-6">
                  <div className="border-l-4 border-[#d82a21] pl-4">
                    <h3 className="text-xl font-bold mb-2">IP Report</h3>
                    <p className="text-gray-600 mb-2">
                      Get comprehensive threat intelligence about an IP address.
                    </p>
                    <p className="text-sm text-gray-500">
                      Endpoint: <code className="bg-slate-100 px-1">/v1/ip/report</code>
                    </p>
                  </div>

                  <div className="border-l-4 border-[#d82a21] pl-4">
                    <h3 className="text-xl font-bold mb-2">Domain Report</h3>
                    <p className="text-gray-600 mb-2">
                      Get threat intelligence about a domain name.
                    </p>
                    <p className="text-sm text-gray-500">
                      Endpoint: <code className="bg-slate-100 px-1">/v1/domain/report</code>
                    </p>
                  </div>

                  <div className="border-l-4 border-[#d82a21] pl-4">
                    <h3 className="text-xl font-bold mb-2">URL Report</h3>
                    <p className="text-gray-600 mb-2">
                      Get threat intelligence about a URL.
                    </p>
                    <p className="text-sm text-gray-500">
                      Endpoint: <code className="bg-slate-100 px-1">/v1/url/report</code>
                    </p>
                  </div>

                  <div className="border-l-4 border-[#d82a21] pl-4">
                    <h3 className="text-xl font-bold mb-2">File Report</h3>
                    <p className="text-gray-600 mb-2">
                      Get threat intelligence about a file hash.
                    </p>
                    <p className="text-sm text-gray-500">
                      Endpoint: <code className="bg-slate-100 px-1">/v1/file/report</code>
                    </p>
                  </div>
                </div>
              </section>

              <section id="rate-limits" className="mb-12">
                <h2 className="text-3xl font-bold mb-4">Rate Limits</h2>
                <p className="text-gray-600 mb-4">
                  API rate limits vary based on your subscription plan:
                </p>
                <ul className="list-disc list-inside space-y-2 text-gray-600">
                  <li>
                    <span className="font-medium">Community Plan:</span> 10 requests per day
                  </li>
                  <li>
                    <span className="font-medium">Enterprise Plan:</span> Customizable based on your needs
                  </li>
                </ul>
              </section>

              <section id="examples" className="mb-12">
                <h2 className="text-3xl font-bold mb-4">Examples</h2>
                <p className="text-gray-600 mb-6">
                  Here are some examples of how to use our API:
                </p>

                <div className="bg-slate-50 p-4 rounded-md my-6">
                  <h4 className="font-bold mb-2">Python Example</h4>
                  <pre className="bg-[#202438] text-white p-4 rounded-md overflow-auto">
{`import requests

API_KEY = "your_api_key"
BASE_URL = "https://api.threatbook.io/v1"

# Get IP report
def get_ip_report(ip_address):
    endpoint = f"{BASE_URL}/ip/report"
    params = {
        "apikey": API_KEY,
        "resource": ip_address
    }

    response = requests.get(endpoint, params=params)
    return response.json()

# Example usage
result = get_ip_report("***********")
print(result)
`}
                  </pre>
                </div>
              </section>

              <section id="faq" className="mb-12">
                <h2 className="text-3xl font-bold mb-4">FAQ</h2>
                <div className="space-y-6">
                  <div>
                    <h4 className="text-xl font-bold mb-2">
                      How do I get an API key?
                    </h4>
                    <p className="text-gray-600">
                      You can sign up for a free Community account or contact our sales team for an Enterprise subscription.
                    </p>
                  </div>

                  <div>
                    <h4 className="text-xl font-bold mb-2">
                      What happens if I exceed my rate limit?
                    </h4>
                    <p className="text-gray-600">
                      You will receive a 429 error response. If you need a higher rate limit, consider upgrading to an Enterprise plan.
                    </p>
                  </div>

                  <div>
                    <h4 className="text-xl font-bold mb-2">
                      Do you offer SDK libraries?
                    </h4>
                    <p className="text-gray-600">
                      Yes, we provide SDK libraries for Python, Java, and Go. Contact our support team for more information.
                    </p>
                  </div>
                </div>
              </section>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Banner */}
      <section className="py-16 bg-[#1b2135] text-white">
        <div className="max-w-[1200px] mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-8">Ready to start using our API?</h2>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link href="/plan">
              <Button className="bg-[#d82a21] hover:bg-[#c82218] text-white rounded-full px-6">
                Choose a Plan
              </Button>
            </Link>
            <Link href="/contactus">
              <Button className="bg-white text-[#1b2135] hover:bg-gray-100 rounded-full px-6">
                Contact Support
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
