import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";

export default function ContactPage() {
  return (
    <div className="bg-white">
      {/* Header */}
      <section className="bg-[#1b2135] text-white py-20">
        <div className="max-w-[1200px] mx-auto px-4 text-center">
          <h1 className="text-5xl font-bold mb-6">Contact Us</h1>
          <p className="text-xl">
            Have questions or need assistance? We're here to help.
          </p>
        </div>
      </section>

      {/* Contact Form */}
      <section className="py-20">
        <div className="max-w-[800px] mx-auto px-4">
          <Card className="p-8 shadow-lg">
            <h2 className="text-2xl font-bold mb-6">Get in Touch</h2>

            <form className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    First Name *
                  </label>
                  <Input
                    type="text"
                    className="w-full border-gray-300"
                    placeholder="Enter your first name"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Last Name *
                  </label>
                  <Input
                    type="text"
                    className="w-full border-gray-300"
                    placeholder="Enter your last name"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Company Email *
                </label>
                <Input
                  type="email"
                  className="w-full border-gray-300"
                  placeholder="<EMAIL>"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Company Name *
                </label>
                <Input
                  type="text"
                  className="w-full border-gray-300"
                  placeholder="Enter your company name"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Phone Number
                </label>
                <Input
                  type="tel"
                  className="w-full border-gray-300"
                  placeholder="Enter your phone number"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  How can we help you? *
                </label>
                <textarea
                  className="w-full min-h-[120px] px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Please describe how we can assist you..."
                  required
                />
              </div>

              <div>
                <Button className="bg-[#d82a21] hover:bg-[#c82218] text-white px-8 py-6 rounded-full">
                  Submit
                </Button>
              </div>
            </form>
          </Card>
        </div>
      </section>

      {/* Contact Information */}
      <section className="py-12 bg-slate-50">
        <div className="max-w-[1200px] mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <h3 className="text-xl font-bold mb-3">Email</h3>
              <p className="text-gray-600"><EMAIL></p>
            </div>

            <div className="text-center">
              <h3 className="text-xl font-bold mb-3">Phone</h3>
              <p className="text-gray-600">+****************</p>
            </div>

            <div className="text-center">
              <h3 className="text-xl font-bold mb-3">Address</h3>
              <p className="text-gray-600">
                123 Security Street<br />
                Cyber City, CS 12345
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
