import type { <PERSON>ada<PERSON> } from "next";
import { ClientBody } from "@/app/ClientBody";
import "./globals.css";
import { Header } from "@/components/header";
import { Footer } from "@/components/footer";

export const metadata: Metadata = {
  title: "Gcandle Intelligence｜Gcandle TI",
  description: "Gcandle TI makes a better understanding of threat intelligence.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className="min-h-screen flex flex-col">
        <ClientBody>
          <Header />
          <main className="flex-1">{children}</main>
          <Footer />
        </ClientBody>
      </body>
    </html>
  );
}
