@font-face {
  font-family: 'Mona-Sans';
  src: url('/fonts/Mona-Sans.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 0 0% 100%;
  --foreground: 240 10% 3.9%;
  --card: 0 0% 100%;
  --card-foreground: 240 10% 3.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 240 10% 3.9%;
  --primary: 240 16% 18%; /* Gcandle dark blue */
  --primary-foreground: 0 0% 98%;
  --secondary: 0 84% 48%; /* Gcandle red */
  --secondary-foreground: 0 0% 98%;
  --muted: 240 4.8% 95.9%;
  --muted-foreground: 240 3.8% 46.1%;
  --accent: 240 4.8% 95.9%;
  --accent-foreground: 240 5.9% 10%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 0 0% 98%;
  --border: 240 5.9% 90%;
  --input: 240 5.9% 90%;
  --ring: 240 5% 64.9%;
  --radius: 0.5rem;
}

.dark {
  --background: 240 16% 18%;
  --foreground: 0 0% 98%;
  --card: 240 16% 18%;
  --card-foreground: 0 0% 98%;
  --popover: 240 16% 18%;
  --popover-foreground: 0 0% 98%;
  --primary: 0 0% 98%;
  --primary-foreground: 240 16% 18%;
  --secondary: 0 84% 48%;
  --secondary-foreground: 0 0% 98%;
  --muted: 240 10% 25%;
  --muted-foreground: 240 5% 64.9%;
  --accent: 240 10% 25%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 85.7% 97.3%;
  --border: 240 10% 25%;
  --input: 240 10% 25%;
  --ring: 240 10% 25%;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: 'Mona-Sans', sans-serif;
  }
}

@layer components {
  .tb-container {
    @apply container mx-auto px-4 md:px-6 max-w-7xl;
  }

  .tb-button-primary {
    @apply bg-[#d82a21] text-white hover:bg-[#c82218] rounded-full px-4 py-2;
  }

  .tb-button-secondary {
    @apply bg-[#202438] text-white hover:bg-[#171b2e] rounded-full px-4 py-2;
  }
}
