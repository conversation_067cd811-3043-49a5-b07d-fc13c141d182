import Image from "next/image";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";

export default function WhitepaperPage() {
  const whitepapers = [
    {
      id: 1,
      title: "Understanding Threat Intelligence Fidelity",
      description: "An in-depth exploration of how <PERSON>can<PERSON>'s high-fidelity threat intelligence helps organizations make better security decisions.",
      category: "Methodology",
      pages: 28,
      image: "/img/home/<USER>",
    },
    {
      id: 2,
      title: "Building an Effective SOC with Threat Intelligence",
      description: "A comprehensive guide to integrating high-quality threat intelligence into security operations centers for enhanced detection and response.",
      category: "Implementation",
      pages: 32,
      image: "/img/home/<USER>",
    },
    {
      id: 3,
      title: "Threat Intelligence in Cloud Security",
      description: "Strategies and best practices for leveraging threat intelligence to secure cloud environments and workloads.",
      category: "Cloud Security",
      pages: 24,
      image: "/img/home/<USER>",
    },
    {
      id: 4,
      title: "Actionable Intelligence for Security Automation",
      description: "How organizations can implement security automation and orchestration using high-fidelity threat intelligence.",
      category: "Automation",
      pages: 30,
      image: "/img/home/<USER>",
    },
    {
      id: 5,
      title: "Advanced Threat Detection Techniques",
      description: "Technical approaches to detecting sophisticated threats using Gcandle's intelligence platform.",
      category: "Technical",
      pages: 36,
      image: "/img/home/<USER>",
    },
    {
      id: 6,
      title: "Measuring ROI from Threat Intelligence Investments",
      description: "A framework for calculating and demonstrating the return on investment from threat intelligence platforms.",
      category: "Business Value",
      pages: 22,
      image: "/img/home/<USER>",
    }
  ];

  return (
    <div className="bg-white">
      {/* Header */}
      <section className="bg-[#1b2135] text-white py-20">
        <div className="max-w-[1200px] mx-auto px-4 text-center">
          <h1 className="text-5xl font-bold mb-6">Gcandle Whitepapers</h1>
          <p className="text-xl mb-10">
            Technical insights and best practices from our security experts
          </p>
        </div>
      </section>

      {/* Featured Whitepaper */}
      <section className="py-16 bg-slate-50">
        <div className="max-w-[1200px] mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
            <div>
              <span className="text-[#d82a21] text-sm font-medium">FEATURED WHITEPAPER</span>
              <h2 className="text-3xl font-bold mt-2 mb-4">Understanding Threat Intelligence Fidelity</h2>
              <p className="text-gray-600 mb-6">
                This comprehensive whitepaper explores the concept of threat intelligence fidelity and why it's
                critical for effective cybersecurity. Learn how Gcandle's approach to high-fidelity
                intelligence helps organizations reduce false positives and focus on real threats.
              </p>
              <div className="flex gap-4">
                <Button className="bg-[#d82a21] hover:bg-[#c82218] text-white rounded-full px-6">
                  Download PDF
                </Button>
                <Button variant="outline" className="border-[#d82a21] text-[#d82a21] hover:bg-red-50 rounded-full px-6">
                  Read Summary
                </Button>
              </div>
            </div>
            <div className="order-first md:order-last">
              <Image
                src="/img/home/<USER>"
                alt="Understanding Threat Intelligence Fidelity"
                width={500}
                height={350}
                className="rounded-lg shadow-md"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Whitepaper Filter */}
      <section className="pt-16 pb-8">
        <div className="max-w-[1200px] mx-auto px-4">
          <div className="flex flex-wrap justify-center gap-4">
            <Button className="bg-[#d82a21] hover:bg-[#c82218] text-white rounded-full">
              All Categories
            </Button>
            <Button variant="outline" className="border-slate-200 text-slate-700 hover:bg-slate-50 rounded-full">
              Methodology
            </Button>
            <Button variant="outline" className="border-slate-200 text-slate-700 hover:bg-slate-50 rounded-full">
              Implementation
            </Button>
            <Button variant="outline" className="border-slate-200 text-slate-700 hover:bg-slate-50 rounded-full">
              Technical
            </Button>
            <Button variant="outline" className="border-slate-200 text-slate-700 hover:bg-slate-50 rounded-full">
              Cloud Security
            </Button>
            <Button variant="outline" className="border-slate-200 text-slate-700 hover:bg-slate-50 rounded-full">
              Business Value
            </Button>
          </div>
        </div>
      </section>

      {/* Whitepapers Grid */}
      <section className="py-8">
        <div className="max-w-[1200px] mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {whitepapers.map((paper) => (
              <Card key={paper.id} className="overflow-hidden shadow-md hover:shadow-lg transition-shadow">
                <div className="h-48 relative">
                  <Image
                    src={paper.image}
                    alt={paper.title}
                    fill
                    className="object-cover"
                  />
                  <div className="absolute top-0 right-0 bg-white m-3 px-2 py-1 text-xs font-medium rounded shadow">
                    {paper.pages} pages
                  </div>
                </div>
                <div className="p-6">
                  <span className="inline-block text-xs font-medium text-[#d82a21] bg-red-50 px-2 py-1 rounded mb-3">
                    {paper.category}
                  </span>
                  <h3 className="text-xl font-bold mb-2">{paper.title}</h3>
                  <p className="text-gray-600 text-sm mb-4">{paper.description}</p>
                  <div className="flex gap-2">
                    <Link href="#" className="text-[#d82a21] text-sm font-medium hover:underline">
                      Download PDF
                    </Link>
                    <span className="text-gray-300">|</span>
                    <Link href="#" className="text-[#d82a21] text-sm font-medium hover:underline">
                      Read Online
                    </Link>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-slate-50 mt-8">
        <div className="max-w-[900px] mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">Request Custom Whitepaper</h2>
          <p className="text-gray-600 mb-8">
            Looking for information on a specific threat intelligence topic? Our security research team
            can create customized whitepapers tailored to your industry or security challenges.
          </p>
          <Button className="bg-[#202438] hover:bg-[#171b2e] text-white rounded-full px-6 py-6">
            Contact Our Research Team
          </Button>
        </div>
      </section>
    </div>
  );
}
