
"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { getResearchCategories, getResearchArticlesByCategory } from "@/lib/content/research";
import { ResearchCard } from "@/components/research/ResearchCard";
import { CategoryFilter } from "@/components/research/CategoryFilter";
import { SearchBar } from "@/components/research/SearchBar";

export default function ThreatResearchPage() {
  const [selectedCategory, setSelectedCategory] = useState("All Research");
  const [visibleCount, setVisibleCount] = useState(6);
  const [searchQuery, setSearchQuery] = useState("");

  const categories = getResearchCategories();
  let filteredArticles = getResearchArticlesByCategory(selectedCategory);

  // Apply search filter
  if (searchQuery) {
    filteredArticles = filteredArticles.filter(article =>
      article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      article.excerpt.toLowerCase().includes(searchQuery.toLowerCase()) ||
      article.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase())) ||
      article.category.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }

  const featuredArticle = filteredArticles[0];
  const otherArticles = filteredArticles.slice(1, visibleCount);
  const hasMoreArticles = filteredArticles.length > visibleCount;

  const handleLoadMore = () => {
    setVisibleCount(prev => prev + 6);
  };

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    setVisibleCount(6); // Reset visible count when category changes
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setVisibleCount(6); // Reset visible count when searching
  };

  return (
    <div className="bg-white">
      {/* Header */}
      <section className="bg-[#1b2135] text-white py-20">
        <div className="max-w-[1200px] mx-auto px-4 text-center">
          <h1 className="text-5xl font-bold mb-6">Threat Research</h1>
          <p className="text-xl mb-8">
            In-depth analysis and intelligence on the latest cybersecurity threats
          </p>
          <div className="max-w-md mx-auto">
            <SearchBar onSearch={handleSearch} placeholder="Search threat research..." />
          </div>
        </div>
      </section>

      {/* Featured Article */}
      {featuredArticle && (
        <section className="py-16">
          <div className="max-w-[1200px] mx-auto px-4">
            <h2 className="text-2xl font-bold mb-8">Featured Research</h2>
            <ResearchCard article={featuredArticle} featured={true} />
          </div>
        </section>
      )}

      {/* Categories */}
      <CategoryFilter
        categories={categories}
        selectedCategory={selectedCategory}
        onCategoryChange={handleCategoryChange}
      />

      {/* Research Articles */}
      <section className="py-16">
        <div className="max-w-[1200px] mx-auto px-4">
          <h2 className="text-2xl font-bold mb-8">
            {searchQuery ? `Search Results for "${searchQuery}"` : 'Latest Research'}
          </h2>

          {otherArticles.length === 0 ? (
            <div className="text-center py-12">
              <div className="max-w-md mx-auto">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">No research found</h3>
                <p className="text-gray-600 mb-6">
                  {searchQuery
                    ? `No research articles match your search for "${searchQuery}". Try different keywords or browse by category.`
                    : 'No research articles found in this category.'
                  }
                </p>
                {searchQuery && (
                  <Button
                    variant="outline"
                    className="border-[#d82a21] text-[#d82a21] hover:bg-red-50 rounded-full px-6"
                    onClick={() => handleSearch('')}
                  >
                    Clear Search
                  </Button>
                )}
              </div>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {otherArticles.map((article) => (
                  <ResearchCard key={article.slug} article={article} />
                ))}
              </div>
              {hasMoreArticles && (
                <div className="text-center mt-12">
                  <Button
                    variant="outline"
                    className="border-[#d82a21] text-[#d82a21] hover:bg-red-50 rounded-full px-6"
                    onClick={handleLoadMore}
                  >
                    Load More Research
                  </Button>
                </div>
              )}
            </>
          )}
        </div>
      </section>

      {/* Newsletter */}
      <section className="py-16 bg-[#1b2135] text-white">
        <div className="max-w-[1200px] mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">Subscribe to Threat Intelligence Updates</h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            Get the latest threat research and intelligence reports delivered to your inbox
          </p>
          <div className="max-w-md mx-auto flex flex-col sm:flex-row gap-4">
            <input
              type="email"
              placeholder="Enter your email"
              className="flex-1 px-4 py-3 rounded-full text-gray-800 focus:outline-none"
            />
            <Button className="bg-[#d82a21] hover:bg-[#c82218] text-white rounded-full px-6 whitespace-nowrap">
              Subscribe
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
