import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Threat Research | Gcandle Intelligence',
  description: 'In-depth analysis and intelligence on the latest cybersecurity threats, APT groups, and attack techniques.',
  openGraph: {
    title: 'Threat Research | Gcandle Intelligence',
    description: 'In-depth analysis and intelligence on the latest cybersecurity threats, APT groups, and attack techniques.',
    type: 'website',
  },
};

export default function ThreatResearchLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
