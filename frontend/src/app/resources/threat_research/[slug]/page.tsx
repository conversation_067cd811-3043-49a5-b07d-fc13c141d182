import { notFound } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { getResearchArticle, getResearchArticles, getAllResearchSlugs } from '@/lib/content/research';
import { ResearchContent } from '@/components/research/ResearchContent';
import { TableOfContents } from '@/components/research/TableOfContents';
import type { Metadata } from 'next';

interface ResearchArticlePageProps {
  params: Promise<{
    slug: string;
  }>;
}

// Generate static params for all research articles
export async function generateStaticParams() {
  const slugs = getAllResearchSlugs();
  return slugs.map((slug) => ({
    slug,
  }));
}

// Generate metadata for each article
export async function generateMetadata({ params }: ResearchArticlePageProps): Promise<Metadata> {
  const { slug } = await params;
  const article = getResearchArticle(slug);

  if (!article) {
    return {
      title: 'Research Not Found',
    };
  }

  return {
    title: `${article.title} | Gcandle Threat Research`,
    description: article.excerpt,
    openGraph: {
      title: article.title,
      description: article.excerpt,
      type: 'article',
      publishedTime: article.date,
      authors: [article.author],
      tags: article.tags,
    },
  };
}

export default async function ResearchArticlePage({ params }: ResearchArticlePageProps) {
  const { slug } = await params;
  const article = getResearchArticle(slug);

  if (!article) {
    notFound();
  }

  // Get related articles (same category, excluding current article)
  const allArticles = getResearchArticles();
  const relatedArticles = allArticles
    .filter(a => a.category === article.category && a.slug !== article.slug)
    .slice(0, 3);

  return (
    <div className="bg-white">
      {/* Article Header */}
      <section className="bg-[#1b2135] text-white py-20">
        <div className="max-w-[1200px] mx-auto px-4">
          <div className="max-w-4xl">
            <Link 
              href="/resources/threat_research" 
              className="inline-flex items-center text-gray-300 hover:text-white mb-6"
            >
              ← Back to Threat Research
            </Link>
            <span className="inline-block text-xs font-medium text-[#d82a21] bg-red-50 bg-opacity-20 px-2 py-1 rounded mb-4">
              {article.category}
            </span>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">{article.title}</h1>
            <p className="text-xl text-gray-300 mb-8">{article.excerpt}</p>
            <div className="flex items-center text-sm text-gray-300">
              <span>{article.date}</span>
              <span className="mx-2">•</span>
              <span>{article.readTime}</span>
              <span className="mx-2">•</span>
              <span>By {article.author}</span>
            </div>
          </div>
        </div>
      </section>

      {/* Article Content */}
      <section className="py-16">
        <div className="max-w-[1200px] mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-12">
            {/* Main Content */}
            <div className="lg:col-span-3">
              <ResearchContent content={article.content} />

              {/* IOCs Section */}
              {article.iocs && article.iocs.length > 0 && (
                <div className="mt-12 p-6 bg-gray-50 rounded-lg">
                  <h3 className="text-xl font-bold mb-4">Indicators of Compromise (IOCs)</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {article.iocs.map((ioc, index) => (
                      <code key={index} className="block bg-white p-2 rounded text-sm font-mono border">
                        {ioc}
                      </code>
                    ))}
                  </div>
                </div>
              )}

              {/* Tags */}
              <div className="mt-8 pt-8 border-t border-gray-200">
                <h4 className="text-sm font-medium text-gray-500 mb-3">TAGS</h4>
                <div className="flex flex-wrap gap-2">
                  {article.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="inline-block bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="sticky top-8">
                {/* Table of Contents */}
                <TableOfContents content={article.content} />

                {/* Author Info */}
                <Card className="p-6 mb-8">
                  <h4 className="font-bold mb-2">About the Author</h4>
                  <p className="text-sm text-gray-600 mb-2">{article.author}</p>
                  <p className="text-xs text-gray-500">{article.authorRole}</p>
                </Card>

                {/* Share */}
                <Card className="p-6 mb-8">
                  <h4 className="font-bold mb-4">Share this Research</h4>
                  <div className="space-y-2">
                    <Button variant="outline" className="w-full justify-start text-sm">
                      Share on LinkedIn
                    </Button>
                    <Button variant="outline" className="w-full justify-start text-sm">
                      Share on Twitter
                    </Button>
                    <Button variant="outline" className="w-full justify-start text-sm">
                      Copy Link
                    </Button>
                  </div>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Related Articles */}
      {relatedArticles.length > 0 && (
        <section className="py-16 bg-slate-50">
          <div className="max-w-[1200px] mx-auto px-4">
            <h2 className="text-2xl font-bold mb-8">Related Research</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {relatedArticles.map((relatedArticle) => (
                <Card key={relatedArticle.slug} className="overflow-hidden shadow-md hover:shadow-lg transition-shadow">
                  <div className="h-48 relative">
                    <Image
                      src={relatedArticle.image}
                      alt={relatedArticle.title}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div className="p-6">
                    <span className="inline-block text-xs font-medium text-[#d82a21] bg-red-50 px-2 py-1 rounded mb-3">
                      {relatedArticle.category}
                    </span>
                    <h3 className="text-lg font-bold mb-2">{relatedArticle.title}</h3>
                    <p className="text-gray-600 text-sm mb-4 line-clamp-3">{relatedArticle.excerpt}</p>
                    <Link 
                      href={`/resources/threat_research/${relatedArticle.slug}`} 
                      className="text-[#d82a21] text-sm font-medium hover:underline"
                    >
                      Read Research →
                    </Link>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* CTA Section */}
      <section className="py-16 bg-[#1b2135] text-white">
        <div className="max-w-[1200px] mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">Stay Updated on Threat Intelligence</h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            Get the latest threat research and intelligence reports delivered to your inbox
          </p>
          <div className="max-w-md mx-auto flex flex-col sm:flex-row gap-4">
            <input
              type="email"
              placeholder="Enter your email"
              className="flex-1 px-4 py-3 rounded-full text-gray-800 focus:outline-none"
            />
            <Button className="bg-[#d82a21] hover:bg-[#c82218] text-white rounded-full px-6 whitespace-nowrap">
              Subscribe
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
