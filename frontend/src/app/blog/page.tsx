import Image from "next/image";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";

export default function BlogPage() {
  const featuredPost = {
    id: 1,
    title: "Understanding the Rise of Ransomware-as-a-Service",
    excerpt: "The cybercriminal ecosystem continues to evolve with Ransomware-as-a-Service becoming increasingly accessible to less technical threat actors. This article explores the implications for organizations and how threat intelligence can help.",
    date: "May 8, 2024",
    author: "<PERSON>",
    authorRole: "Principal Threat Researcher",
    image: "/img/home/<USER>",
    readTime: "7 min read",
    category: "Ransomware"
  };

  const blogPosts = [
    {
      id: 2,
      title: "5 Ways to Integrate Threat Intelligence into Your Security Operations",
      excerpt: "Organizations often struggle to operationalize threat intelligence effectively. Here are five practical approaches to integrating intelligence into your daily security operations.",
      date: "May 2, 2024",
      author: "<PERSON>",
      authorRole: "Head of Security Operations",
      image: "/img/home/<USER>",
      readTime: "5 min read",
      category: "Best Practices"
    },
    {
      id: 3,
      title: "The Growing Threat of Supply Chain Attacks",
      excerpt: "Supply chain attacks have become a preferred method for sophisticated threat actors. We analyze recent incidents and provide recommendations for securing your software supply chain.",
      date: "April 25, 2024",
      author: "<PERSON> Wong",
      authorRole: "Security Analyst",
      image: "/img/home/<USER>",
      readTime: "8 min read",
      category: "Threat Analysis"
    },
    {
      id: 4,
      title: "API Security: The Next Frontier in Cybersecurity",
      excerpt: "As organizations increasingly rely on APIs for digital transformation, they're becoming a prime target for attackers. Learn about the latest API attack vectors and protection strategies.",
      date: "April 18, 2024",
      author: "Emma Rodriguez",
      authorRole: "Cloud Security Specialist",
      image: "/img/home/<USER>",
      readTime: "6 min read",
      category: "Cloud Security"
    },
    {
      id: 5,
      title: "The Role of AI in Modern Threat Intelligence",
      excerpt: "Artificial intelligence is transforming how we collect, analyze, and apply threat intelligence. This post explores the benefits and limitations of AI-powered threat intelligence platforms.",
      date: "April 12, 2024",
      author: "James Smith",
      authorRole: "AI Research Lead",
      image: "/img/home/<USER>",
      readTime: "9 min read",
      category: "Technology"
    },
    {
      id: 6,
      title: "Zero Trust Architecture: Beyond the Buzzword",
      excerpt: "Zero Trust has become a popular security concept, but implementation remains challenging. We break down practical steps to adopt zero trust principles using threat intelligence.",
      date: "April 5, 2024",
      author: "Lisa Park",
      authorRole: "Strategic Security Advisor",
      image: "/img/home/<USER>",
      readTime: "7 min read",
      category: "Strategy"
    },
    {
      id: 7,
      title: "Threat Hunting with Gcandle Intelligence",
      excerpt: "Proactive threat hunting is essential for detecting sophisticated attackers. Learn how Gcandle's high-fidelity intelligence can enhance your threat hunting capabilities.",
      date: "March 28, 2024",
      author: "Robert Zhang",
      authorRole: "Senior Threat Hunter",
      image: "/img/about/cti-diagram.png",
      readTime: "10 min read",
      category: "Threat Hunting"
    }
  ];

  const categories = [
    "All Posts",
    "Ransomware",
    "Best Practices",
    "Threat Analysis",
    "Cloud Security",
    "Technology",
    "Strategy",
    "Threat Hunting"
  ];

  return (
    <div className="bg-white">
      {/* Header */}
      <section className="bg-[#1b2135] text-white py-20">
        <div className="max-w-[1200px] mx-auto px-4 text-center">
          <h1 className="text-5xl font-bold mb-6">Gcandle Blog</h1>
          <p className="text-xl">
            Insights and perspectives from our cybersecurity experts
          </p>
        </div>
      </section>

      {/* Featured Post */}
      <section className="py-16">
        <div className="max-w-[1200px] mx-auto px-4">
          <h2 className="text-2xl font-bold mb-8">Featured Article</h2>
          <div className="grid grid-cols-1 lg:grid-cols-5 gap-8">
            <div className="lg:col-span-3 order-2 lg:order-1">
              <span className="inline-block text-xs font-medium text-[#d82a21] bg-red-50 px-2 py-1 rounded mb-3">
                {featuredPost.category}
              </span>
              <h3 className="text-3xl font-bold mb-4">{featuredPost.title}</h3>
              <p className="text-gray-600 mb-6">{featuredPost.excerpt}</p>
              {/* <div className="flex items-center mb-6">
                <div className="w-10 h-10 bg-gray-300 rounded-full mr-3"></div>
                <div>
                  <p className="font-medium">{featuredPost.author}</p>
                  <p className="text-sm text-gray-500">{featuredPost.authorRole}</p>
                </div>
              </div> */}
              <div className="flex items-center text-sm text-gray-500 mb-6">
                <span>{featuredPost.date}</span>
                <span className="mx-2">•</span>
                <span>{featuredPost.readTime}</span>
              </div>
              <Link href="#">
                <Button className="bg-[#d82a21] hover:bg-[#c82218] text-white rounded-full px-6">
                  Read Full Article
                </Button>
              </Link>
            </div>
            <div className="lg:col-span-2 order-1 lg:order-2">
              <div className="relative h-[300px] lg:h-full rounded-lg overflow-hidden">
                <Image
                  src={featuredPost.image}
                  alt={featuredPost.title}
                  fill
                  className="object-cover"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Categories */}
      <section className="py-8 bg-slate-50">
        <div className="max-w-[1200px] mx-auto px-4">
          <div className="flex flex-wrap justify-center gap-4">
            {categories.map((category, index) => (
              <Button
                key={index}
                variant={index === 0 ? "default" : "outline"}
                className={
                  index === 0
                    ? "bg-[#d82a21] hover:bg-[#c82218] text-white rounded-full"
                    : "border-slate-200 text-slate-700 hover:bg-slate-50 rounded-full"
                }
              >
                {category}
              </Button>
            ))}
          </div>
        </div>
      </section>

      {/* Blog Posts */}
      <section className="py-16">
        <div className="max-w-[1200px] mx-auto px-4">
          <h2 className="text-2xl font-bold mb-8">Latest Articles</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {blogPosts.map((post) => (
              <Card key={post.id} className="overflow-hidden shadow-md hover:shadow-lg transition-shadow">
                <div className="h-48 relative">
                  <Image
                    src={post.image}
                    alt={post.title}
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="p-6">
                  <span className="inline-block text-xs font-medium text-[#d82a21] bg-red-50 px-2 py-1 rounded mb-3">
                    {post.category}
                  </span>
                  <h3 className="text-xl font-bold mb-2">{post.title}</h3>
                  <p className="text-gray-600 text-sm mb-4 line-clamp-3">{post.excerpt}</p>
                  <div className="flex items-center text-xs text-gray-500 mb-4">
                    <span>{post.date}</span>
                    <span className="mx-2">•</span>
                    <span>{post.readTime}</span>
                  </div>
                  <Link href="#" className="text-[#d82a21] text-sm font-medium hover:underline">
                    Read Article →
                  </Link>
                </div>
              </Card>
            ))}
          </div>
          <div className="text-center mt-12">
            <Button variant="outline" className="border-[#d82a21] text-[#d82a21] hover:bg-red-50 rounded-full px-6">
              Load More Articles
            </Button>
          </div>
        </div>
      </section>

      {/* Newsletter */}
      <section className="py-16 bg-[#1b2135] text-white">
        <div className="max-w-[1200px] mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">Subscribe to Our Newsletter</h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            Get the latest threat intelligence insights and cybersecurity updates delivered to your inbox
          </p>
          <div className="max-w-md mx-auto flex flex-col sm:flex-row gap-4">
            <input
              type="email"
              placeholder="Enter your email"
              className="flex-1 px-4 py-3 rounded-full text-gray-800 focus:outline-none"
            />
            <Button className="bg-[#d82a21] hover:bg-[#c82218] text-white rounded-full px-6 whitespace-nowrap">
              Subscribe
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
