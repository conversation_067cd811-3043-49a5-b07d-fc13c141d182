import Image from "next/image";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ThreatIntelDemo } from "@/components/ThreatIntelDemo";

export default function DemoPage() {
  return (
    <div className="bg-white">
      {/* Header */}
      <section className="bg-[#1b2135] text-white py-20">
        <div className="max-w-[1200px] mx-auto px-4 text-center">
          <h1 className="text-5xl font-bold mb-6">Interactive Platform Demo</h1>
          <p className="text-xl mb-4">
            Experience Gcandle's high-fidelity threat intelligence in action
          </p>
          <p className="text-gray-400 max-w-2xl mx-auto">
            Try our interactive demo to see how Gcandle TI provides actionable insights
            for security teams. No sign-up required.
          </p>
        </div>
      </section>

      {/* Demo Introduction */}
      <section className="py-16">
        <div className="max-w-[1200px] mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold mb-4">Experience Gcandle TI</h2>
              <p className="text-gray-600 mb-6">
                Our interactive demo gives you a taste of Gcandle's threat intelligence platform capabilities.
                Analyze malicious IPs, domains, URLs, and file hashes to see how our platform delivers:
              </p>
              <ul className="space-y-3 mb-6">
                <li className="flex items-start">
                  <span className="inline-flex items-center justify-center p-1 bg-red-100 text-red-800 rounded-full mr-3 mt-1">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <polyline points="20 6 9 17 4 12"></polyline>
                    </svg>
                  </span>
                  <span><strong>High-fidelity intelligence</strong> with minimal false positives</span>
                </li>
                <li className="flex items-start">
                  <span className="inline-flex items-center justify-center p-1 bg-red-100 text-red-800 rounded-full mr-3 mt-1">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <polyline points="20 6 9 17 4 12"></polyline>
                    </svg>
                  </span>
                  <span><strong>Detailed attribution</strong> including threat actor tactics and techniques</span>
                </li>
                <li className="flex items-start">
                  <span className="inline-flex items-center justify-center p-1 bg-red-100 text-red-800 rounded-full mr-3 mt-1">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <polyline points="20 6 9 17 4 12"></polyline>
                    </svg>
                  </span>
                  <span><strong>Actionable insights</strong> for faster detection and response</span>
                </li>
                <li className="flex items-start">
                  <span className="inline-flex items-center justify-center p-1 bg-red-100 text-red-800 rounded-full mr-3 mt-1">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <polyline points="20 6 9 17 4 12"></polyline>
                    </svg>
                  </span>
                  <span><strong>Related indicators</strong> to expand your investigation</span>
                </li>
              </ul>
              <p className="text-gray-600">
                Note: This is a simulated demo with pre-populated data. The full platform offers
                real-time intelligence on millions of threats globally.
              </p>
            </div>
            <div className="order-first md:order-last">
              <Image
                src="/img/home/<USER>"
                alt="Gcandle TI Platform"
                width={550}
                height={400}
                className="rounded-lg shadow-md"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Interactive Demo */}
      <ThreatIntelDemo />

      {/* Platform Features */}
      <section className="py-16 bg-slate-50">
        <div className="max-w-[1200px] mx-auto px-4">
          <h2 className="text-3xl font-bold mb-4 text-center">Complete Platform Capabilities</h2>
          <p className="text-center text-gray-600 mb-12 max-w-3xl mx-auto">
            Our interactive demo showcases a subset of Gcandle TI's capabilities.
            The full platform includes these additional features:
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white p-6 rounded-lg shadow-sm border border-slate-200">
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#d82a21" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="12" cy="12" r="10"></circle>
                  <polygon points="16.24 7.76 14.12 14.12 7.76 16.24 9.88 9.88 16.24 7.76"></polygon>
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-2">Global Threat Coverage</h3>
              <p className="text-gray-600">
                Access intelligence on millions of threat indicators across all geographies,
                with real-time updates and historical context.
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border border-slate-200">
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#d82a21" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                  <polyline points="22,6 12,13 2,6"></polyline>
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-2">Threat Feeds</h3>
              <p className="text-gray-600">
                Specialized intelligence feeds targeting specific industries, geographies,
                or threat types, delivered in formats that integrate with your security tools.
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border border-slate-200">
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#d82a21" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                  <line x1="3" y1="9" x2="21" y2="9"></line>
                  <line x1="9" y1="21" x2="9" y2="9"></line>
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-2">Custom API Access</h3>
              <p className="text-gray-600">
                RESTful APIs with flexible rate limits for seamless integration with your
                SIEM, SOAR, TIP, or custom security applications.
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border border-slate-200">
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#d82a21" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                  <polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline>
                  <line x1="12" y1="22.08" x2="12" y2="12"></line>
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-2">Threat Hunting</h3>
              <p className="text-gray-600">
                Advanced search capabilities for proactive threat hunting,
                with query builders and saved searches for common threat patterns.
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border border-slate-200">
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#d82a21" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                  <line x1="8" y1="21" x2="16" y2="21"></line>
                  <line x1="12" y1="17" x2="12" y2="21"></line>
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-2">Custom Dashboards</h3>
              <p className="text-gray-600">
                Configurable dashboards and visual analytics to monitor your threat landscape
                and track security metrics that matter to your organization.
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border border-slate-200">
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#d82a21" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                  <circle cx="9" cy="7" r="4"></circle>
                  <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                  <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-2">Threat Actor Profiles</h3>
              <p className="text-gray-600">
                Detailed profiles of known threat actors, including their TTPs, targets,
                campaign history, and attribution confidence levels.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16">
        <div className="max-w-[900px] mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">Ready to Experience the Full Platform?</h2>
          <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
            Schedule a personalized demo with our security experts to see how Gcandle TI
            can enhance your security operations with actionable threat intelligence.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link href="/plan">
              <Button className="bg-[#d82a21] hover:bg-[#c82218] text-white rounded-full px-8 py-6">
                Get Started Free
              </Button>
            </Link>
            <Link href="/contactus">
              <Button variant="outline" className="border-[#d82a21] text-[#d82a21] hover:bg-red-50 rounded-full px-8 py-6">
                Request Full Demo
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
