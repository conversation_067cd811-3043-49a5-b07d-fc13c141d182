"use client";

import Link from "next/link";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader } from "@/components/ui/card";
import { Check, X, Star, Users, Shield, Zap, ArrowRight, CreditCard } from "lucide-react";
import { useAuth } from "@/hooks/useAuth";

// Pricing data structure
const pricingPlans = [
  {
    id: "free",
    name: "Community",
    description: "Perfect for individual security researchers and small teams getting started",
    price: { monthly: 0, annual: 0 },
    popular: false,
    features: [
      { name: "10 intelligence searches per day", included: true },
      { name: "Basic threat intelligence data", included: true },
      { name: "Community API access", included: true },
      { name: "Email support", included: true },
      { name: "Basic reporting", included: true },
      { name: "Advanced analytics", included: false },
      { name: "Premium API endpoints", included: false },
      { name: "Priority support", included: false },
      { name: "Custom integrations", included: false },
      { name: "Dedicated account manager", included: false },
    ],
    cta: "Get Started Free",
    ctaVariant: "outline" as const,
  },
  {
    id: "professional",
    name: "Professional",
    description: "Ideal for growing security teams and SOC analysts",
    price: { monthly: 99, annual: 990 },
    popular: true,
    features: [
      { name: "1,000 intelligence searches per day", included: true },
      { name: "Advanced threat intelligence data", included: true },
      { name: "Full API access", included: true },
      { name: "Priority email & chat support", included: true },
      { name: "Advanced reporting & analytics", included: true },
      { name: "Custom dashboards", included: true },
      { name: "Threat hunting tools", included: true },
      { name: "Integration support", included: true },
      { name: "Custom integrations", included: false },
      { name: "Dedicated account manager", included: false },
    ],
    cta: "Start Professional",
    ctaVariant: "default" as const,
  },
  {
    id: "enterprise",
    name: "Enterprise",
    description: "For large organizations requiring unlimited access and custom solutions",
    price: { monthly: 299, annual: 2990 },
    popular: false,
    features: [
      { name: "Unlimited intelligence searches", included: true },
      { name: "Premium threat intelligence data", included: true },
      { name: "Full API access + premium endpoints", included: true },
      { name: "24/7 phone & email support", included: true },
      { name: "Advanced reporting & analytics", included: true },
      { name: "Custom dashboards", included: true },
      { name: "Advanced threat hunting tools", included: true },
      { name: "Custom integrations", included: true },
      { name: "Dedicated account manager", included: true },
      { name: "SLA guarantees", included: true },
    ],
    cta: "Contact Sales",
    ctaVariant: "secondary" as const,
  },
];

const testimonials = [
  {
    name: "Sarah Chen",
    role: "Security Analyst",
    company: "TechCorp",
    content: "Gcandle TI has transformed our threat detection capabilities. The intelligence quality is exceptional.",
    rating: 5,
  },
  {
    name: "Michael Rodriguez",
    role: "CISO",
    company: "FinanceSecure",
    content: "The API integration was seamless, and the support team is incredibly responsive.",
    rating: 5,
  },
];

export default function PlanPage() {
  const [billingCycle, setBillingCycle] = useState<"monthly" | "annual">("monthly");
  const { isAuthenticated, user } = useAuth();

  const handlePlanSelection = (planId: string) => {
    if (planId === "free") {
      // Redirect to signup if not authenticated, otherwise to dashboard
      if (!isAuthenticated) {
        window.location.href = "/signup";
      } else {
        window.location.href = "/profile";
      }
    } else if (planId === "enterprise") {
      window.location.href = "/contactus";
    } else {
      // Redirect to payment flow (to be implemented)
      console.log(`Selected plan: ${planId}, billing: ${billingCycle}`);
      // TODO: Implement Stripe checkout
    }
  };

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-slate-50 to-blue-50">
        <div className="max-w-[1200px] mx-auto px-4 text-center">
          <h1 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-[#202438] to-[#d82a21] bg-clip-text text-transparent">
            Choose Your Plan
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Select the perfect plan for your threat intelligence needs. From individual researchers to enterprise security teams.
          </p>

          {/* Billing Toggle */}
        </div>
      </section>

      {/* Pricing Cards */}
      <section className="py-20">
        <div className="max-w-[1200px] mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {pricingPlans.map((plan) => (
              <Card
                key={plan.id}
                className={`relative overflow-hidden transition-all duration-300 hover:shadow-xl ${
                  plan.popular
                    ? "border-[#d82a21] shadow-lg scale-105 lg:scale-110"
                    : "border-gray-200 hover:border-gray-300"
                }`}
              >
                {plan.popular && (
                  <div className="absolute top-0 left-0 right-0 bg-gradient-to-r from-[#d82a21] to-[#ff4444] text-white text-center py-2 text-sm font-medium">
                    <Star className="inline-block w-4 h-4 mr-1" />
                    Most Popular
                  </div>
                )}

                <CardHeader className={`text-center ${plan.popular ? "pt-12" : "pt-8"}`}>
                  <div className="mb-4">
                    <h3 className="text-2xl font-bold text-gray-900">{plan.name}</h3>
                    <p className="text-gray-600 mt-2">{plan.description}</p>
                  </div>

                  <div className="mb-6">
                    {plan.price.monthly === null ? (
                      <div className="text-4xl font-bold text-gray-900">Custom</div>
                    ) : plan.price.monthly === 0 ? (
                      <div className="text-4xl font-bold text-gray-900">Free</div>
                    ) : (
                      <div>
                        <div className="text-4xl font-bold text-gray-900">
                          ${billingCycle === "monthly" ? plan.price.monthly : Math.floor(plan.price.annual! / 12)}
                          <span className="text-lg font-normal text-gray-600">/month</span>
                        </div>
                        {billingCycle === "annual" && plan.price.annual && (
                          <div className="text-sm text-gray-500 mt-1">
                            Billed annually (${plan.price.annual}/year)
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  <Button
                    onClick={() => handlePlanSelection(plan.id)}
                    variant={plan.ctaVariant}
                    className={`w-full rounded-full py-3 ${
                      plan.popular
                        ? "bg-[#d82a21] hover:bg-[#c82218] text-white"
                        : plan.ctaVariant === "outline"
                        ? "border-[#202438] text-[#202438] hover:bg-[#202438] hover:text-white"
                        : "bg-[#202438] hover:bg-[#171b2e] text-white"
                    }`}
                  >
                    {plan.cta}
                    <ArrowRight className="ml-2 w-4 h-4" />
                  </Button>
                </CardHeader>

                <CardContent className="px-6 pb-8">
                  <ul className="space-y-4">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-start">
                        {feature.included ? (
                          <Check className="text-green-500 mr-3 h-5 w-5 mt-0.5 flex-shrink-0" />
                        ) : (
                          <X className="text-gray-300 mr-3 h-5 w-5 mt-0.5 flex-shrink-0" />
                        )}
                        <span className={feature.included ? "text-gray-900" : "text-gray-400"}>
                          {feature.name}
                        </span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Feature Comparison Table */}
      <section className="py-20 bg-slate-50">
        <div className="max-w-[1200px] mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Compare Plans</h2>
            <p className="text-xl text-gray-600">
              See exactly what's included in each plan
            </p>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full bg-white rounded-xl shadow-lg overflow-hidden">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Features</th>
                  <th className="px-6 py-4 text-center text-sm font-medium text-gray-900">Community</th>
                  <th className="px-6 py-4 text-center text-sm font-medium text-gray-900 bg-red-50">
                    Professional
                    <span className="block text-xs text-[#d82a21] font-normal">Most Popular</span>
                  </th>
                  <th className="px-6 py-4 text-center text-sm font-medium text-gray-900">Enterprise</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {[
                  { feature: "Intelligence searches per day", free: "10", pro: "1,000", enterprise: "Unlimited" },
                  { feature: "API access", free: "Basic", pro: "Full", enterprise: "Premium" },
                  { feature: "Support", free: "Email", pro: "Priority", enterprise: "24/7 Phone" },
                  { feature: "Advanced analytics", free: false, pro: true, enterprise: true },
                  { feature: "Custom dashboards", free: false, pro: true, enterprise: true },
                  { feature: "Threat hunting tools", free: false, pro: true, enterprise: true },
                  { feature: "Custom integrations", free: false, pro: false, enterprise: true },
                  { feature: "Dedicated account manager", free: false, pro: false, enterprise: true },
                ].map((row, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 text-sm font-medium text-gray-900">{row.feature}</td>
                    <td className="px-6 py-4 text-center text-sm text-gray-600">
                      {typeof row.free === "boolean" ? (
                        row.free ? <Check className="w-5 h-5 text-green-500 mx-auto" /> : <X className="w-5 h-5 text-gray-300 mx-auto" />
                      ) : (
                        row.free
                      )}
                    </td>
                    <td className="px-6 py-4 text-center text-sm text-gray-600 bg-red-50">
                      {typeof row.pro === "boolean" ? (
                        row.pro ? <Check className="w-5 h-5 text-green-500 mx-auto" /> : <X className="w-5 h-5 text-gray-300 mx-auto" />
                      ) : (
                        row.pro
                      )}
                    </td>
                    <td className="px-6 py-4 text-center text-sm text-gray-600">
                      {typeof row.enterprise === "boolean" ? (
                        row.enterprise ? <Check className="w-5 h-5 text-green-500 mx-auto" /> : <X className="w-5 h-5 text-gray-300 mx-auto" />
                      ) : (
                        row.enterprise
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </section>

      {/* Social Proof / Testimonials */}
      <section className="py-20">
        <div className="max-w-[1200px] mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Trusted by Security Teams</h2>
            <p className="text-xl text-gray-600">
              Join thousands of security professionals who rely on Gcandle TI
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="p-8 shadow-md">
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-gray-600 mb-6 italic">"{testimonial.content}"</p>
                <div>
                  <p className="font-semibold text-gray-900">{testimonial.name}</p>
                  <p className="text-sm text-gray-500">{testimonial.role} at {testimonial.company}</p>
                </div>
              </Card>
            ))}
          </div>

          {/* Trust indicators */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <Users className="w-12 h-12 text-[#d82a21] mx-auto mb-4" />
              <p className="text-2xl font-bold text-gray-900">10,000+</p>
              <p className="text-gray-600">Active Users</p>
            </div>
            <div>
              <Shield className="w-12 h-12 text-[#d82a21] mx-auto mb-4" />
              <p className="text-2xl font-bold text-gray-900">99.9%</p>
              <p className="text-gray-600">Uptime SLA</p>
            </div>
            <div>
              <Zap className="w-12 h-12 text-[#d82a21] mx-auto mb-4" />
              <p className="text-2xl font-bold text-gray-900">1B+</p>
              <p className="text-gray-600">Threat Indicators</p>
            </div>
            <div>
              <CreditCard className="w-12 h-12 text-[#d82a21] mx-auto mb-4" />
              <p className="text-2xl font-bold text-gray-900">SOC 2</p>
              <p className="text-gray-600">Compliant</p>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-slate-50">
        <div className="max-w-[1200px] mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Frequently Asked Questions</h2>
            <p className="text-xl text-gray-600">
              Everything you need to know about our pricing and plans
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {[
              {
                question: "What payment methods do you accept?",
                answer: "We accept all major credit cards (Visa, MasterCard, American Express) and PayPal. Enterprise customers can also pay via bank transfer or purchase orders."
              },
              {
                question: "Can I change my plan anytime?",
                answer: "Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately, and we'll prorate any billing differences."
              },
              {
                question: "Is there a free trial for paid plans?",
                answer: "Yes! All paid plans come with a 14-day free trial. No credit card required to start your trial."
              },
              {
                question: "What happens if I exceed my usage limits?",
                answer: "We'll notify you when you're approaching your limits. You can upgrade your plan or purchase additional credits to continue using the service."
              },
              {
                question: "Do you offer discounts for annual billing?",
                answer: "Yes! Annual billing saves you 17% compared to monthly billing. You'll see the discounted price when you select annual billing."
              },
              {
                question: "Can I cancel my subscription anytime?",
                answer: "Absolutely. You can cancel your subscription at any time from your account settings. You'll continue to have access until the end of your billing period."
              }
            ].map((faq, index) => (
              <Card key={index} className="p-6 shadow-md">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">{faq.question}</h3>
                <p className="text-gray-600">{faq.answer}</p>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-[#202438] to-[#d82a21]">
        <div className="max-w-[1200px] mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            Ready to enhance your threat intelligence?
          </h2>
          <p className="text-xl text-gray-200 mb-8 max-w-2xl mx-auto">
            Join thousands of security professionals who trust Gcandle TI for their threat intelligence needs.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              onClick={() => handlePlanSelection("free")}
              variant="outline"
              className="bg-white text-[#202438] hover:bg-gray-100 border-white rounded-full px-8 py-3"
            >
              Start Free
            </Button>
            <Button
              onClick={() => handlePlanSelection("professional")}
              className="bg-[#d82a21] hover:bg-[#c82218] text-white rounded-full px-8 py-3"
            >
              Try Professional Free
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
