import Image from "next/image";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";

export default function AboutPage() {
  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="bg-[#1b2135] text-white py-20">
        <div className="max-w-[1200px] mx-auto px-4 text-center">
          <p className="text-lg mb-2">Our Mission</p>
          <h1 className="text-5xl font-bold mb-4">Secure The Digital World</h1>
          <p className="text-xl">Fight against cyber threats. Empower digital transformation</p>
        </div>
      </section>

      {/* About Gcandle */}
      <section className="py-20">
        <div className="max-w-[1200px] mx-auto px-4">
          <p className="text-[#d82a21] mb-2">About Gcandle</p>
          <h2 className="text-3xl font-bold mb-12">Guarding your cyber-security in this digital era</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <div>
              <p className="text-gray-600 mb-6">
                Established in 2015, Gcandle has been an innovative player in the
                cyber security industry, continually delivering precise, efficient, and
                intelligent solutions for cyber threat detection and response. Gcandle
                takes the pioneering role in developing cyber threat intelligence, providing
                comprehensive protection on cloud, traffic, and endpoints. Gcandle aims
                to arm its customers with a lifelong threat monitoring system and security
                response capabilities.
              </p>
              <p className="text-gray-600">
                With Gcandle, customers in industries such as government, energy,
                finance, intelligent manufacturing, and the Internet benefit from the
                next generation of security solutions.
              </p>
            </div>
            <div className="flex justify-center">
              <Image
                src="/img/about/diagram.png"
                alt="Gcandle Security diagram"
                width={386}
                height={386}
                className="max-w-full h-auto"
              />
            </div>
          </div>
        </div>
      </section>

      {/* What is Gcandle TI */}
      <section className="py-20 bg-slate-50">
        <div className="max-w-[1200px] mx-auto px-4">
          <p className="text-[#d82a21] mb-2">What is Gcandle TI</p>
          <h2 className="text-3xl font-bold mb-12">
            Gcandle TI is designed to help SOC teams to improve their productivity
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <div className="order-2 md:order-1">
              <Image
                src="/img/about/cti-diagram.png"
                alt="Gcandle TI"
                width={587}
                height={324}
                className="max-w-full h-auto"
              />
            </div>
            <div className="order-1 md:order-2">
              <p className="text-gray-600 mb-8">
                Gcandle TI relies on Gcandle's powerful security intelligence cloud
                and high-quality intelligence extraction system, proven by commercial customers,
                to deliver high-fidelity, efficient, and actionable threat intelligence. With
                Gcandle TI, enterprises can effectively detect new traps, reduce alarm noise,
                and discover new threats. Gcandle TI aims to help the security operation teams
                stay ahead of any threat.
              </p>
              <Link href="#">
                <Button className="bg-[#d82a21] hover:bg-[#c82218] rounded-full px-6">
                  Try It for Free
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Rewards & Honours */}
      <section className="py-20">
        <div className="max-w-[1200px] mx-auto px-4">
          <p className="text-[#d82a21] mb-2 text-center">Rewards & Honours</p>
          <h2 className="text-3xl font-bold mb-12 text-center">
            Trusted by the Industry's Leading Authorities, Analysts, and Associations
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-8">
            {/* Award 1 */}
            <div className="flex flex-col items-center text-center">
              <Image
                src="/img/about/reward1.png"
                alt="Gartner"
                width={160}
                height={130}
                className="mb-4 h-32 w-auto"
              />
              <p className="text-gray-600 text-sm">
                Listed in Gartner's Market Guide for Security Threat Intelligence Products and
                Services 4 consecutive times
              </p>
            </div>

            {/* Award 2 */}
            <div className="flex flex-col items-center text-center">
              <Image
                src="/img/about/reward2.png"
                alt="Frost & Sullivan"
                width={154}
                height={125}
                className="mb-4 h-32 w-auto"
              />
              <p className="text-gray-600 text-sm">
                Ranked No.1 in the leader quadrant growth index in Frost & Sullivan's 2022
                China Threat Intelligence Market Report
              </p>
            </div>

            {/* Award 3 */}
            <div className="flex flex-col items-center text-center">
              <Image
                src="/img/about/reward3.png"
                alt="Red Herring"
                width={154}
                height={125}
                className="mb-4 h-32 w-auto"
              />
              <p className="text-gray-600 text-sm">
                Red Herring Top 100 Asia Winner(2019)
              </p>
            </div>

            {/* Award 4 */}
            <div className="flex flex-col items-center text-center">
              <Image
                src="/img/about/reward4.png"
                alt="Cybersecurity 500"
                width={154}
                height={125}
                className="mb-4 h-32 w-auto"
              />
              <p className="text-gray-600 text-sm">
                Cybersecurity 500<br />(2017-2019)
              </p>
            </div>

            {/* Award 5 */}
            <div className="flex flex-col items-center text-center">
              <Image
                src="/img/about/reward5.png"
                alt="Global Infosec Awards"
                width={160}
                height={120}
                className="mb-4 h-32 w-auto"
              />
              <p className="text-gray-600 text-sm">
                Global Infosec Awards<br />(2018-2022)
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Banner */}
      <section className="py-16 bg-[#1b2135] text-white">
        <div className="max-w-[1200px] mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-8">Speed up your work with Gcandle Intelligence</h2>
          <Link href="/contactus">
            <Button className="bg-white text-[#1b2135] hover:bg-gray-100 rounded-full px-6">
              Talk to Us
            </Button>
          </Link>
        </div>
      </section>
    </div>
  );
}
