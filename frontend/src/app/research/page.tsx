import Image from "next/image";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";

export default function ResearchPage() {
  const reports = [
    {
      id: 1,
      title: "2024 Global Threat Intelligence Report",
      description: "A comprehensive analysis of the global threat landscape, highlighting emerging threats, attack patterns, and defense strategies.",
      date: "April 15, 2024",
      image: "/img/home/<USER>",
      category: "Annual Report"
    },
    {
      id: 2,
      title: "Ransomware Trends and Tactics in 2024",
      description: "An in-depth look at the evolution of ransomware attacks, including new variants, targeting strategies, and recommended security measures.",
      date: "March 8, 2024",
      image: "/img/home/<USER>",
      category: "Threat Analysis"
    },
    {
      id: 3,
      title: "APT Groups Targeting Critical Infrastructure",
      description: "Analysis of recent advanced persistent threat campaigns targeting energy, healthcare, and government sectors.",
      date: "February 22, 2024",
      image: "/img/home/<USER>",
      category: "Threat Actor"
    },
    {
      id: 4,
      title: "Supply Chain Security: Lessons from Recent Breaches",
      description: "Key insights and recommendations based on major supply chain attacks and their impact on global organizations.",
      date: "January 15, 2024",
      image: "/img/home/<USER>",
      category: "Case Study"
    },
    {
      id: 5,
      title: "Emerging Threats in Cloud Environments",
      description: "Analysis of new attack vectors and security challenges in cloud infrastructure and services.",
      date: "December 10, 2023",
      image: "/img/home/<USER>",
      category: "Cloud Security"
    },
    {
      id: 6,
      title: "Financial Sector Threat Landscape",
      description: "A sector-specific analysis of threats targeting banking, insurance, and financial services organizations.",
      date: "November 5, 2023",
      image: "/img/home/<USER>",
      category: "Industry Report"
    }
  ];

  return (
    <div className="bg-white">
      {/* Header */}
      <section className="bg-[#1b2135] text-white py-20">
        <div className="max-w-[1200px] mx-auto px-4 text-center">
          <h1 className="text-5xl font-bold mb-6">Gcandle Research</h1>
          <p className="text-xl mb-10">
            Insights, reports, and analysis from our threat intelligence team
          </p>
        </div>
      </section>

      {/* Featured Report */}
      <section className="py-16 bg-slate-50">
        <div className="max-w-[1200px] mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
            <div>
              <span className="text-[#d82a21] text-sm font-medium">FEATURED</span>
              <h2 className="text-3xl font-bold mt-2 mb-4">2024 Global Threat Intelligence Report</h2>
              <p className="text-gray-600 mb-6">
                Our comprehensive annual report provides an in-depth analysis of the global threat landscape,
                emerging trends, and predictions for the coming year. Based on data from millions of threat
                intelligence sources, this report offers actionable insights for security professionals.
              </p>
              <Button className="bg-[#d82a21] hover:bg-[#c82218] text-white rounded-full px-6">
                Download Report
              </Button>
            </div>
            <div className="order-first md:order-last">
              <Image
                src="/img/home/<USER>"
                alt="2024 Global Threat Intelligence Report"
                width={500}
                height={350}
                className="rounded-lg shadow-md"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Research Reports */}
      <section className="py-16">
        <div className="max-w-[1200px] mx-auto px-4">
          <h2 className="text-3xl font-bold mb-2 text-center">Research Reports</h2>
          <p className="text-gray-600 mb-12 text-center max-w-2xl mx-auto">
            Explore our collection of threat intelligence reports, whitepapers, and research studies
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {reports.map((report) => (
              <Card key={report.id} className="overflow-hidden shadow-md hover:shadow-lg transition-shadow">
                <div className="h-48 relative">
                  <Image
                    src={report.image}
                    alt={report.title}
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="p-6">
                  <div className="flex justify-between items-center mb-3">
                    <span className="text-xs font-medium text-[#d82a21] bg-red-50 px-2 py-1 rounded">
                      {report.category}
                    </span>
                    <span className="text-xs text-gray-500">{report.date}</span>
                  </div>
                  <h3 className="text-xl font-bold mb-2">{report.title}</h3>
                  <p className="text-gray-600 text-sm mb-4">{report.description}</p>
                  <Link href="#" className="text-[#d82a21] text-sm font-medium hover:underline">
                    Read Report →
                  </Link>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Banner */}
      <section className="py-16 bg-[#1b2135] text-white">
        <div className="max-w-[1200px] mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">Stay Informed About New Research</h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            Subscribe to our newsletter to receive the latest threat intelligence reports and research papers
          </p>
          <div className="max-w-md mx-auto flex flex-col sm:flex-row gap-4">
            <input
              type="email"
              placeholder="Enter your email"
              className="flex-1 px-4 py-3 rounded-full text-gray-800 focus:outline-none"
            />
            <Button className="bg-[#d82a21] hover:bg-[#c82218] text-white rounded-full px-6 whitespace-nowrap">
              Subscribe
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
