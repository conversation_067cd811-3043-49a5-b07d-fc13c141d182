"use client";

import Link from "next/link";
import Image from "next/image";
import { Linkedin, Twitter, Slack } from "lucide-react";
import { ChatWindow } from "@/components/ChatWindow";

export function Footer() {
  return (
    <footer className="bg-[#1e2133] text-white pt-12 pb-6">
      <div className="max-w-[1200px] mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 pb-8">
          {/* Logo section */}
          <div className="col-span-1">
            <h3 className="text-sm font-semibold mb-4">Gcandle TI</h3>

          </div>

          {/* Plans section */}
          <div className="col-span-1">
            <h3 className="text-sm font-semibold mb-4">Plans</h3>
            <ul className="space-y-3">
              <li>
                <Link href="/plan" className="text-sm text-gray-300 hover:text-white">
                  Community(Always free)
                </Link>
              </li>
              <li>
                <Link href="/plan" className="text-sm text-gray-300 hover:text-white">
                  Enterprise
                </Link>
              </li>
            </ul>
          </div>

          {/* Resources section */}
          <div className="col-span-1">
            <h3 className="text-sm font-semibold mb-4">Resources</h3>
            <ul className="space-y-3">
              <li>
                <Link href="/blog" className="text-sm text-gray-300 hover:text-white">
                  Blog
                </Link>
              </li>
              <li>
                <Link href="/resources/threat_research" className="text-sm text-gray-300 hover:text-white">
                  ThreatResearch
                </Link>
              </li>
              <li>
                <Link href="/whitepaper" className="text-sm text-gray-300 hover:text-white">
                  Whitepaper
                </Link>
              </li>
              <li>
                <Link href="/research" className="text-sm text-gray-300 hover:text-white">
                  Research
                </Link>
              </li>
            </ul>
          </div>

          {/* Company section */}
          <div className="col-span-1">
            <div className="mb-6">
              <h3 className="text-sm font-semibold mb-4">Company</h3>
              <ul className="space-y-3">
                <li>
                  <Link href="/about" className="text-sm text-gray-300 hover:text-white">
                    About Gcandle
                  </Link>
                </li>
                <li>
                  <Link href="/contactus" className="text-sm text-gray-300 hover:text-white">
                    Contact Us
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-sm font-semibold mb-3">Join us online</h3>
              <div className="flex space-x-4">
                <Link
                  href="https://www.linkedin.com/company/threatbook/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-300 hover:text-white"
                >
                  <Linkedin className="h-5 w-5" />
                </Link>
                <Link
                  href="https://twitter.com/GcandleLabs"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-300 hover:text-white"
                >
                  <Twitter className="h-5 w-5" />
                </Link>
                <Slack className="h-5 w-5 text-gray-300" />
              </div>
            </div>
          </div>
        </div>

        {/* Copyright */}
        <div className="pt-8 border-t border-gray-800 text-center sm:text-left text-xs text-gray-500">
          <p>
            <EMAIL> All Rights Reserved. &nbsp;&nbsp;
            <Link href="/terms" className="hover:text-gray-300">Terms</Link>
            &nbsp;|&nbsp;
            <Link href="/privacy" className="hover:text-gray-300">Privacy</Link>
          </p>
        </div>
      </div>

      {/* Chat Window Component */}
      <ChatWindow />
    </footer>
  );
}
