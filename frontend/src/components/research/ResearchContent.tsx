import ReactMarkdown from 'react-markdown';

interface ResearchContentProps {
  content: string;
}

export function ResearchContent({ content }: ResearchContentProps) {
  return (
    <div className="prose prose-lg max-w-none">
      <ReactMarkdown
        components={{
          h1: ({ children }) => (
            <h1 className="text-3xl font-bold mb-6 text-gray-900">{children}</h1>
          ),
          h2: ({ children }) => {
            const id = String(children)
              .toLowerCase()
              .replace(/[^\w\s-]/g, '')
              .replace(/\s+/g, '-')
              .replace(/-+/g, '-')
              .trim();
            return (
              <h2 id={id} className="text-2xl font-bold mb-4 mt-8 text-gray-900 scroll-mt-20">
                {children}
              </h2>
            );
          },
          h3: ({ children }) => {
            const id = String(children)
              .toLowerCase()
              .replace(/[^\w\s-]/g, '')
              .replace(/\s+/g, '-')
              .replace(/-+/g, '-')
              .trim();
            return (
              <h3 id={id} className="text-xl font-bold mb-3 mt-6 text-gray-900 scroll-mt-20">
                {children}
              </h3>
            );
          },
          h4: ({ children }) => {
            const id = String(children)
              .toLowerCase()
              .replace(/[^\w\s-]/g, '')
              .replace(/\s+/g, '-')
              .replace(/-+/g, '-')
              .trim();
            return (
              <h4 id={id} className="text-lg font-bold mb-2 mt-4 text-gray-900 scroll-mt-20">
                {children}
              </h4>
            );
          },
          p: ({ children }) => (
            <p className="mb-4 text-gray-700 leading-relaxed">{children}</p>
          ),
          ul: ({ children }) => (
            <ul className="mb-4 ml-6 list-disc text-gray-700">{children}</ul>
          ),
          ol: ({ children }) => (
            <ol className="mb-4 ml-6 list-decimal text-gray-700">{children}</ol>
          ),
          li: ({ children }) => (
            <li className="mb-1">{children}</li>
          ),
          code: ({ children }) => (
            <code className="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono text-gray-800">
              {children}
            </code>
          ),
          pre: ({ children }) => (
            <pre className="bg-gray-900 text-white p-4 rounded-lg overflow-x-auto mb-4">
              {children}
            </pre>
          ),
          blockquote: ({ children }) => (
            <blockquote className="border-l-4 border-[#d82a21] pl-4 italic text-gray-600 mb-4">
              {children}
            </blockquote>
          ),
          table: ({ children }) => (
            <div className="overflow-x-auto mb-4">
              <table className="min-w-full border border-gray-300">{children}</table>
            </div>
          ),
          thead: ({ children }) => (
            <thead className="bg-gray-50">{children}</thead>
          ),
          tbody: ({ children }) => (
            <tbody>{children}</tbody>
          ),
          tr: ({ children }) => (
            <tr className="border-b border-gray-200">{children}</tr>
          ),
          th: ({ children }) => (
            <th className="px-4 py-2 text-left font-medium text-gray-900 border-r border-gray-300 last:border-r-0">
              {children}
            </th>
          ),
          td: ({ children }) => (
            <td className="px-4 py-2 text-gray-700 border-r border-gray-300 last:border-r-0">
              {children}
            </td>
          ),
          img: ({ src, alt }) => {
            // Convert relative paths to absolute paths for research assets
            let imageSrc = src;
            if (src && !src.startsWith('http') && !src.startsWith('/')) {
              // For research images, prepend the research path
              imageSrc = `/research/DarkHotel组织最新RPC攻击组件披露/${src}`;
            }
            return (
              <div className="my-6">
                <img
                  src={imageSrc}
                  alt={alt || ''}
                  className="max-w-full h-auto rounded-lg shadow-md mx-auto"
                />
                {alt && (
                  <p className="text-sm text-gray-500 text-center mt-2 italic">{alt}</p>
                )}
              </div>
            );
          },
          a: ({ href, children }) => (
            <a 
              href={href} 
              className="text-[#d82a21] hover:underline"
              target={href?.startsWith('http') ? '_blank' : undefined}
              rel={href?.startsWith('http') ? 'noopener noreferrer' : undefined}
            >
              {children}
            </a>
          ),
          strong: ({ children }) => (
            <strong className="font-bold text-gray-900">{children}</strong>
          ),
          em: ({ children }) => (
            <em className="italic">{children}</em>
          ),
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
}
