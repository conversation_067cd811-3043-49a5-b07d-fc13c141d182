import Image from "next/image";
import Link from "next/link";
import { Card } from "@/components/ui/card";
import { ResearchArticle } from "@/lib/content/research";

interface ResearchCardProps {
  article: ResearchArticle;
  featured?: boolean;
}

export function ResearchCard({ article, featured = false }: ResearchCardProps) {
  if (featured) {
    return (
      <div className="grid grid-cols-1 lg:grid-cols-5 gap-8">
        <div className="lg:col-span-3 order-2 lg:order-1">
          <span className="inline-block text-xs font-medium text-[#d82a21] bg-red-50 px-2 py-1 rounded mb-3">
            {article.category}
          </span>
          <h3 className="text-3xl font-bold mb-4">{article.title}</h3>
          <p className="text-gray-600 mb-6">{article.excerpt}</p>
          <div className="flex items-center text-sm text-gray-500 mb-6">
            <span>{article.date}</span>
            <span className="mx-2">•</span>
            <span>{article.readTime}</span>
          </div>
          <Link 
            href={`/resources/threat_research/${article.slug}`}
            className="inline-block bg-[#d82a21] hover:bg-[#c82218] text-white rounded-full px-6 py-3 font-medium transition-colors"
          >
            Read Full Research
          </Link>
        </div>
        <div className="lg:col-span-2 order-1 lg:order-2">
          <div className="relative h-[300px] lg:h-full rounded-lg overflow-hidden">
            <Image
              src={article.image}
              alt={article.title}
              fill
              className="object-cover"
            />
          </div>
        </div>
      </div>
    );
  }

  return (
    <Card className="overflow-hidden shadow-md hover:shadow-lg transition-shadow">
      <div className="h-48 relative">
        <Image
          src={article.image}
          alt={article.title}
          fill
          className="object-cover"
        />
      </div>
      <div className="p-6">
        <span className="inline-block text-xs font-medium text-[#d82a21] bg-red-50 px-2 py-1 rounded mb-3">
          {article.category}
        </span>
        <h3 className="text-xl font-bold mb-2">{article.title}</h3>
        <p className="text-gray-600 text-sm mb-4 line-clamp-3">{article.excerpt}</p>
        <div className="flex items-center text-xs text-gray-500 mb-4">
          <span>{article.date}</span>
          <span className="mx-2">•</span>
          <span>{article.readTime}</span>
        </div>
        <Link 
          href={`/resources/threat_research/${article.slug}`} 
          className="text-[#d82a21] text-sm font-medium hover:underline"
        >
          Read Research →
        </Link>
      </div>
    </Card>
  );
}
