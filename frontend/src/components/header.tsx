/**
 * Header Component
 *
 * Main navigation header with authentication integration.
 * Supports both desktop and mobile layouts with AWS Cognito authentication.
 */

"use client";

import Link from "next/link";
import Image from "next/image";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Search, ChevronDown, Menu, X, User, LogOut, AlertCircle } from "lucide-react";
import { useAuth } from "@/hooks/useAuth";
import { redirectToSignIn, redirectToSignUp, formatUserDisplayName } from "@/lib/auth/auth-utils";

export function Header() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [mobileSearchOpen, setMobileSearchOpen] = useState(false);
  const [isActionLoading, setIsActionLoading] = useState(false);

  // Use authentication context
  const { isAuthenticated, isLoading: authLoading, user, signOut, error, clearError } = useAuth();

  // Handle sign in button click
  const handleSignIn = async () => {
    try {
      setIsActionLoading(true);
      clearError(); // Clear any previous errors
      await redirectToSignIn();
    } catch (error) {
      console.error("❌ Sign in redirect failed:", error);
      setIsActionLoading(false);
    }
  };

  // Handle sign up button click
  const handleSignUp = async () => {
    try {
      setIsActionLoading(true);
      clearError(); // Clear any previous errors
      await redirectToSignUp();
    } catch (error) {
      console.error("❌ Sign up redirect failed:", error);
      setIsActionLoading(false);
    }
  };

  // Handle sign out button click
  const handleSignOut = async () => {
    try {
      setIsActionLoading(true);
      await signOut();
    } catch (error) {
      console.error("❌ Sign out failed:", error);
    } finally {
      setIsActionLoading(false);
    }
  };

  // Get user display name
  const getUserDisplayName = () => {
    return formatUserDisplayName(user);
  };

  // Loading state for buttons
  const isButtonLoading = isActionLoading || authLoading;

  return (
    <header className="sticky top-0 z-50 w-full bg-white border-b border-slate-200/80">
      <div className="flex items-center justify-between px-4 h-16 max-w-[1200px] mx-auto">
        <div className="flex items-center">
          <Link href="/" className="mr-4">
            <Image
              src="/img/logo.svg"
              alt="Gcandle"
              width={146}
              height={16}
              className="h-4 w-auto"
            />
          </Link>
          <div className="hidden lg:block ml-4">
            <div className="relative w-[400px]">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-4 w-4 text-slate-400" />
              </div>
              <Input
                type="text"
                placeholder="Search for IP/Domain intelligence"
                className="pl-10 h-10 bg-slate-50 border-slate-200 rounded-full focus:ring-0"
              />
            </div>
          </div>
        </div>

        <div className="hidden lg:flex items-center space-x-6">
          <nav className="flex items-center space-x-6">
            <Link
              href="/api"
              className="text-sm font-medium text-slate-700 hover:text-slate-900"
            >
              API
            </Link>
            <DropdownMenu>
              <DropdownMenuTrigger className="flex items-center text-sm font-medium text-slate-700 hover:text-slate-900">
                Resources
                <ChevronDown className="ml-1 h-4 w-4" />
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem asChild>
                  <Link href="/blog">Blog</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/resources/threat_research">ThreatResearch</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/whitepaper">Whitepaper</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/research">Research</Link>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <Link
              href="/plan"
              className="text-sm font-medium text-slate-700 hover:text-slate-900"
            >
              Plan
            </Link>
            <Link
              href="/about"
              className="text-sm font-medium text-slate-700 hover:text-slate-900"
            >
              About
            </Link>
          </nav>
          <div className="flex items-center space-x-2">
            {/* Error indicator */}
            {error && (
              <div className="flex items-center text-red-600 text-sm mr-2">
                <AlertCircle className="h-4 w-4 mr-1" />
                <span className="hidden sm:inline">Auth Error</span>
              </div>
            )}

            {isAuthenticated ? (
              // Authenticated state: Show user info and sign out button
              <div className="flex items-center space-x-2">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="outline"
                      className="flex items-center space-x-2 bg-white hover:bg-slate-50 text-slate-900 border border-slate-200 rounded-sm"
                      disabled={isButtonLoading}
                    >
                      <User className="h-4 w-4" />
                      <span>{getUserDisplayName()}</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem asChild>
                      <Link href="/profile">Profile</Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={handleSignOut} disabled={isButtonLoading}>
                      <LogOut className="h-4 w-4 mr-2" />
                      {isActionLoading ? "Signing out..." : "Sign out"}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            ) : (
              // Unauthenticated state: Show sign in and sign up buttons
              <>
                <Button
                  onClick={handleSignIn}
                  disabled={isButtonLoading}
                  // variant="outline"
                  className="bg-[#202438] hover:bg-[#171b2e] text-white border-none rounded-sm"
                >
                  {isActionLoading ? "Loading..." : "Sign in"}
                </Button>
                {/* <Button
                  onClick={handleSignUp}
                  disabled={isButtonLoading}
                  className="bg-white hover:bg-slate-50 text-slate-900 border border-slate-200 rounded-sm"
                >
                  {isActionLoading ? "Loading..." : "Sign up"}
                </Button> */}
              </>
            )}
          </div>
        </div>

        <div className="flex lg:hidden items-center space-x-4">
          <button
            onClick={() => setMobileSearchOpen(true)}
            className="p-2 text-slate-600"
          >
            <Search className="h-5 w-5" />
          </button>
          <button
            onClick={() => setMobileMenuOpen(true)}
            className="p-2 text-slate-600"
          >
            <Menu className="h-5 w-5" />
          </button>
        </div>

        {/* Mobile search modal */}
        {mobileSearchOpen && (
          <div className="fixed inset-0 bg-black/50 z-50">
            <div className="bg-white p-4 pt-8">
              <div className="relative">
                <Input
                  type="text"
                  autoFocus
                  placeholder="Search for IP/Domain Intelligence..."
                  className="h-12"
                />
                <button
                  className="absolute right-3 top-3"
                  onClick={() => setMobileSearchOpen(false)}
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Mobile navigation menu */}
        {mobileMenuOpen && (
          <div className="fixed inset-0 bg-black/50 z-50">
            <div className="bg-white h-full w-full p-4 flex flex-col">
              <div className="flex items-center justify-between mb-8">
                <Link href="/" onClick={() => setMobileMenuOpen(false)}>
                  <Image
                    src="/img/logo.svg"
                    alt="ThreatBook"
                    width={146}
                    height={16}
                    className="h-4 w-auto"
                  />
                </Link>
                <button onClick={() => setMobileMenuOpen(false)}>
                  <X className="h-6 w-6" />
                </button>
              </div>
              <nav className="flex flex-col space-y-6">
                <Link
                  href="/api"
                  className="text-lg font-medium"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  API
                </Link>
                <div className="text-lg font-medium">
                  Resources
                </div>
                <Link
                  href="/plan"
                  className="text-lg font-medium"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  Plan
                </Link>
                <Link
                  href="/about"
                  className="text-lg font-medium"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  About
                </Link>
              </nav>
              <div className="mt-auto py-4 flex flex-col space-y-3">
                {/* Error indicator for mobile */}
                {error && (
                  <div className="flex items-center justify-center text-red-600 text-sm mb-2 p-2 bg-red-50 rounded-sm">
                    <AlertCircle className="h-4 w-4 mr-1" />
                    <span>Authentication Error</span>
                  </div>
                )}

                {isAuthenticated ? (
                  // Authenticated state: Show user info and sign out button
                  <>
                    <div className="flex items-center justify-center mb-2 p-2 border border-slate-200 rounded-sm">
                      <User className="h-5 w-5 mr-2 text-slate-600" />
                      <span className="text-slate-800">{getUserDisplayName()}</span>
                    </div>
                    <Button
                      onClick={() => {
                        setMobileMenuOpen(false);
                        handleSignOut();
                      }}
                      disabled={isButtonLoading}
                      variant="outline"
                      className="w-full flex items-center justify-center bg-white hover:bg-slate-50 text-slate-900 border border-slate-200 rounded-sm"
                    >
                      <LogOut className="h-4 w-4 mr-2" />
                      {isActionLoading ? "Signing out..." : "Sign out"}
                    </Button>
                  </>
                ) : (
                  // Unauthenticated state: Show sign in and sign up buttons
                  <>
                    <Button
                      onClick={() => {
                        setMobileMenuOpen(false);
                        handleSignIn();
                      }}
                      disabled={isButtonLoading}
                      variant="outline"
                      className="w-full bg-[#202438] hover:bg-[#171b2e] text-white border-none rounded-sm"
                    >
                      {isActionLoading ? "Loading..." : "Sign in"}
                    </Button>
                    <Button
                      onClick={() => {
                        setMobileMenuOpen(false);
                        handleSignUp();
                      }}
                      disabled={isButtonLoading}
                      className="w-full bg-white hover:bg-slate-50 text-slate-900 border border-slate-200 rounded-sm"
                    >
                      {isActionLoading ? "Loading..." : "Sign up"}
                    </Button>
                  </>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
}
