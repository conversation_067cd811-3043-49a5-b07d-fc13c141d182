"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Check, X, Star, ArrowRight } from "lucide-react";

export interface PricingFeature {
  name: string;
  included: boolean;
}

export interface PricingPlan {
  id: string;
  name: string;
  description: string;
  price: {
    monthly: number | null;
    annual: number | null;
  };
  popular: boolean;
  features: PricingFeature[];
  cta: string;
  ctaVariant: "default" | "outline" | "secondary";
}

interface PricingCardProps {
  plan: PricingPlan;
  billingCycle: "monthly" | "annual";
  onSelect: (planId: string) => void;
  className?: string;
}

export function PricingCard({ plan, billingCycle, onSelect, className = "" }: PricingCardProps) {
  const getPrice = () => {
    if (plan.price.monthly === null) return "Custom";
    if (plan.price.monthly === 0) return "Free";
    
    const price = billingCycle === "monthly" 
      ? plan.price.monthly 
      : Math.floor(plan.price.annual! / 12);
    
    return `$${price}`;
  };

  const getButtonStyles = () => {
    if (plan.popular) {
      return "bg-[#d82a21] hover:bg-[#c82218] text-white";
    }
    if (plan.ctaVariant === "outline") {
      return "border-[#202438] text-[#202438] hover:bg-[#202438] hover:text-white";
    }
    return "bg-[#202438] hover:bg-[#171b2e] text-white";
  };

  return (
    <Card
      className={`relative overflow-hidden transition-all duration-300 hover:shadow-xl ${
        plan.popular
          ? "border-[#d82a21] shadow-lg scale-105 lg:scale-110"
          : "border-gray-200 hover:border-gray-300"
      } ${className}`}
    >
      {plan.popular && (
        <div className="absolute top-0 left-0 right-0 bg-gradient-to-r from-[#d82a21] to-[#ff4444] text-white text-center py-2 text-sm font-medium">
          <Star className="inline-block w-4 h-4 mr-1" />
          Most Popular
        </div>
      )}
      
      <CardHeader className={`text-center ${plan.popular ? "pt-12" : "pt-8"}`}>
        <div className="mb-4">
          <h3 className="text-2xl font-bold text-gray-900">{plan.name}</h3>
          <p className="text-gray-600 mt-2">{plan.description}</p>
        </div>
        
        <div className="mb-6">
          <div className="text-4xl font-bold text-gray-900">
            {getPrice()}
            {plan.price.monthly !== null && plan.price.monthly > 0 && (
              <span className="text-lg font-normal text-gray-600">/month</span>
            )}
          </div>
          {billingCycle === "annual" && plan.price.annual && plan.price.monthly && plan.price.monthly > 0 && (
            <div className="text-sm text-gray-500 mt-1">
              Billed annually (${plan.price.annual}/year)
            </div>
          )}
        </div>
        
        <Button
          onClick={() => onSelect(plan.id)}
          variant={plan.ctaVariant}
          className={`w-full rounded-full py-3 ${getButtonStyles()}`}
        >
          {plan.cta}
          <ArrowRight className="ml-2 w-4 h-4" />
        </Button>
      </CardHeader>
      
      <CardContent className="px-6 pb-8">
        <ul className="space-y-4">
          {plan.features.map((feature, index) => (
            <li key={index} className="flex items-start">
              {feature.included ? (
                <Check className="text-green-500 mr-3 h-5 w-5 mt-0.5 flex-shrink-0" />
              ) : (
                <X className="text-gray-300 mr-3 h-5 w-5 mt-0.5 flex-shrink-0" />
              )}
              <span className={feature.included ? "text-gray-900" : "text-gray-400"}>
                {feature.name}
              </span>
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  );
}
