"use client";

import { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  CreditCard, 
  Calendar, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  Settings,
  Loader2
} from "lucide-react";
import { useSubscription } from "@/hooks/useSubscription";
import type { Subscription, SubscriptionStatus } from "@/lib/stripe/stripe-config";

interface SubscriptionCardProps {
  className?: string;
}

export function SubscriptionCard({ className = "" }: SubscriptionCardProps) {
  const { 
    subscription, 
    isLoading, 
    error, 
    isSubscribed, 
    isPro, 
    isEnterprise,
    openPortal,
    cancelSub 
  } = useSubscription();
  
  const [isProcessing, setIsProcessing] = useState(false);

  const handleManageBilling = async () => {
    setIsProcessing(true);
    try {
      await openPortal();
    } catch (err) {
      console.error('Failed to open billing portal:', err);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleCancelSubscription = async () => {
    if (!confirm('Are you sure you want to cancel your subscription? You will lose access to premium features at the end of your billing period.')) {
      return;
    }

    setIsProcessing(true);
    try {
      await cancelSub();
    } catch (err) {
      console.error('Failed to cancel subscription:', err);
    } finally {
      setIsProcessing(false);
    }
  };

  const getStatusBadge = (status: SubscriptionStatus) => {
    const statusConfig = {
      active: { label: 'Active', variant: 'default' as const, icon: CheckCircle },
      trialing: { label: 'Trial', variant: 'secondary' as const, icon: Calendar },
      canceled: { label: 'Canceled', variant: 'destructive' as const, icon: XCircle },
      past_due: { label: 'Past Due', variant: 'destructive' as const, icon: AlertTriangle },
      incomplete: { label: 'Incomplete', variant: 'destructive' as const, icon: AlertTriangle },
      incomplete_expired: { label: 'Expired', variant: 'destructive' as const, icon: XCircle },
      unpaid: { label: 'Unpaid', variant: 'destructive' as const, icon: AlertTriangle },
    };

    const config = statusConfig[status] || statusConfig.active;
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="w-3 h-3" />
        {config.label}
      </Badge>
    );
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getPlanDisplayName = () => {
    if (isEnterprise) return 'Enterprise';
    if (isPro) return 'Professional';
    return 'Community (Free)';
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin" />
          <span className="ml-2">Loading subscription...</span>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="py-8">
          <div className="flex items-center text-red-600">
            <AlertTriangle className="w-5 h-5 mr-2" />
            <span>Error loading subscription: {error}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <CreditCard className="w-5 h-5" />
            Subscription
          </div>
          {subscription && getStatusBadge(subscription.status)}
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Current Plan */}
        <div>
          <h3 className="font-semibold text-lg mb-2">{getPlanDisplayName()}</h3>
          {!isSubscribed ? (
            <p className="text-gray-600">
              You're currently on the free Community plan with basic features.
            </p>
          ) : (
            <div className="space-y-2">
              <p className="text-gray-600">
                You have access to all {subscription?.planName} features.
              </p>
              
              {subscription && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                  <div className="flex items-center text-sm text-gray-600">
                    <Calendar className="w-4 h-4 mr-2" />
                    <span>
                      Current period: {formatDate(subscription.currentPeriodStart)} - {formatDate(subscription.currentPeriodEnd)}
                    </span>
                  </div>
                  
                  {subscription.trialEnd && subscription.status === 'trialing' && (
                    <div className="flex items-center text-sm text-blue-600">
                      <AlertTriangle className="w-4 h-4 mr-2" />
                      <span>
                        Trial ends: {formatDate(subscription.trialEnd)}
                      </span>
                    </div>
                  )}
                  
                  {subscription.cancelAtPeriodEnd && (
                    <div className="flex items-center text-sm text-orange-600">
                      <AlertTriangle className="w-4 h-4 mr-2" />
                      <span>
                        Cancels on: {formatDate(subscription.currentPeriodEnd)}
                      </span>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Usage Summary */}
        <div className="border-t pt-4">
          <h4 className="font-medium mb-3">Current Usage Limits</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Daily Searches:</span>
              <div className="font-medium">
                {isEnterprise ? 'Unlimited' : isPro ? '1,000' : '10'}
              </div>
            </div>
            <div>
              <span className="text-gray-600">API Access:</span>
              <div className="font-medium">
                {isEnterprise ? 'Premium' : isPro ? 'Full' : 'Basic'}
              </div>
            </div>
            <div>
              <span className="text-gray-600">Support:</span>
              <div className="font-medium">
                {isEnterprise ? '24/7 Phone' : isPro ? 'Priority' : 'Email'}
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="border-t pt-4 space-y-3">
          {!isSubscribed ? (
            <div className="space-y-2">
              <Button 
                asChild 
                className="w-full bg-[#d82a21] hover:bg-[#c82218]"
              >
                <a href="/plan">Upgrade to Professional</a>
              </Button>
              <p className="text-xs text-gray-500 text-center">
                Get more searches, advanced features, and priority support
              </p>
            </div>
          ) : (
            <div className="flex flex-col sm:flex-row gap-2">
              <Button
                onClick={handleManageBilling}
                disabled={isProcessing}
                variant="outline"
                className="flex-1"
              >
                {isProcessing ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Settings className="w-4 h-4 mr-2" />
                )}
                Manage Billing
              </Button>
              
              {!subscription?.cancelAtPeriodEnd && (
                <Button
                  onClick={handleCancelSubscription}
                  disabled={isProcessing}
                  variant="outline"
                  className="flex-1 text-red-600 hover:text-red-700"
                >
                  Cancel Subscription
                </Button>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
