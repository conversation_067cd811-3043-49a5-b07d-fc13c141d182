"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Search } from "lucide-react";

// Define the threat sample type
type ThreatSample = {
  id: string;
  type: string;
  value: string;
  risk: string;
  tags: string[];
  firstSeen: string;
  lastSeen: string;
  description: string;
  relatedIoCs: string[];
  location?: string;
  registrar?: string;
  fileType?: string;
  status?: string;
};

// Demo data
const threatSamples: ThreatSample[] = [
  {
    id: "ip-1",
    type: "IP",
    value: "**************",
    risk: "high",
    tags: ["Botnet", "Malware Distribution", "Command & Control"],
    location: "Russia",
    firstSeen: "2023-09-12",
    lastSeen: "2024-05-08",
    description: "This IP address is associated with botnet infrastructure and malware distribution campaigns.",
    relatedIoCs: ["example.malicious-domain.com", "fc5e635a236eb2faaad3c8b56c5fe19c"],
  },
  {
    id: "domain-1",
    type: "Domain",
    value: "malicious-update.com",
    risk: "critical",
    tags: ["Phishing", "Data Exfiltration", "Fake Update"],
    registrar: "NameCheap, Inc.",
    firstSeen: "2024-01-15",
    lastSeen: "2024-05-07",
    description: "This domain is part of a phishing campaign that targets financial institutions.",
    relatedIoCs: ["************", "b4e6c84a4d7c823b2883140e19b4318a"],
  },
  {
    id: "file-1",
    type: "File Hash",
    value: "8f31e8df49188b25a83a6cf2e79dd804",
    risk: "medium",
    tags: ["Trojan", "Information Stealer"],
    fileType: "Windows Executable",
    firstSeen: "2023-11-22",
    lastSeen: "2024-04-30",
    description: "This file is a Trojan that steals credentials from browsers and email clients.",
    relatedIoCs: ["command-server.xyz", "*************"],
  },
  {
    id: "url-1",
    type: "URL",
    value: "http://download.fake-software.com/update.exe",
    risk: "high",
    tags: ["Malware", "Drive-by Download"],
    status: "Active",
    firstSeen: "2024-02-28",
    lastSeen: "2024-05-01",
    description: "This URL hosts malicious software disguised as legitimate updates.",
    relatedIoCs: ["download.fake-software.com", "c3eb4c8a9e7f1d2b5a6d8c7f9a2b1d3e"],
  }
];

// Map risk levels to colors
const riskColors = {
  low: "bg-yellow-100 text-yellow-800",
  medium: "bg-orange-100 text-orange-800",
  high: "bg-red-100 text-red-800",
  critical: "bg-purple-100 text-purple-800",
};

export function ThreatIntelDemo() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedThreat, setSelectedThreat] = useState<ThreatSample>(threatSamples[0]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [activeTab, setActiveTab] = useState("overview");

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();

    if (searchTerm.trim()) {
      setIsAnalyzing(true);
      setShowResults(false);

      // Simulate API call delay
      setTimeout(() => {
        const foundThreat = threatSamples.find(
          threat => threat.value.toLowerCase().includes(searchTerm.toLowerCase())
        ) || threatSamples[0];

        setSelectedThreat(foundThreat);
        setIsAnalyzing(false);
        setShowResults(true);
      }, 1500);
    }
  };

  const handleSampleClick = (sample: ThreatSample) => {
    setSearchTerm(sample.value);
    setIsAnalyzing(true);
    setShowResults(false);

    // Simulate API call delay
    setTimeout(() => {
      setSelectedThreat(sample);
      setIsAnalyzing(false);
      setShowResults(true);
    }, 800);
  };

  return (
    <div className="w-full bg-slate-50 py-16">
      <div className="max-w-[1200px] mx-auto px-4">
        <h2 className="text-3xl font-bold mb-3 text-center">Try Gcandle Intelligence</h2>
        <p className="text-center text-gray-600 mb-10 max-w-3xl mx-auto">
          See how our threat intelligence platform provides high-fidelity, actionable insights.
          Try searching for known malicious indicators or click on one of our examples below.
        </p>

        {/* Search Bar */}
        <div className="max-w-2xl mx-auto mb-8">
          <form onSubmit={handleSearch} className="relative">
            <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-slate-400" />
            </div>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search IP, domain, URL, or file hash..."
              className="pl-12 pr-28 py-4 w-full rounded-full border-slate-200 focus:ring-[#d82a21] focus:border-[#d82a21] focus:outline-none"
            />
            <Button
              type="submit"
              className="absolute right-2 top-2 bg-[#d82a21] hover:bg-[#c82218] text-white rounded-full"
            >
              Analyze
            </Button>
          </form>
        </div>

        {/* Sample Searches */}
        <div className="flex flex-wrap justify-center gap-2 mb-10">
          <p className="text-sm text-gray-500 w-full text-center mb-2">Try these examples:</p>
          {threatSamples.map((sample) => (
            <Button
              key={sample.id}
              variant="outline"
              className="text-xs border-slate-200 hover:bg-slate-100 rounded-full"
              onClick={() => handleSampleClick(sample)}
            >
              {sample.value}
            </Button>
          ))}
        </div>

        {/* Analysis Progress */}
        {isAnalyzing && (
          <div className="max-w-4xl mx-auto text-center py-20">
            <div className="inline-block w-12 h-12 border-4 border-t-[#d82a21] border-r-[#d82a21] border-b-transparent border-l-transparent rounded-full animate-spin mb-4"></div>
            <p className="text-lg font-medium">Analyzing threat intelligence data...</p>
            <p className="text-sm text-gray-500">Searching across global threat databases</p>
          </div>
        )}

        {/* Results */}
        {showResults && (
          <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-md overflow-hidden">
            {/* Result Header */}
            <div className="bg-[#1b2135] text-white p-6">
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                <div>
                  <div className="flex items-center gap-2 mb-1">
                    <span className="text-sm bg-white/10 rounded px-2 py-0.5">{selectedThreat.type}</span>
                    <span className={`text-sm px-2 py-0.5 rounded ${
                      selectedThreat.risk === 'low' ? 'bg-yellow-600' :
                      selectedThreat.risk === 'medium' ? 'bg-orange-600' :
                      selectedThreat.risk === 'high' ? 'bg-red-600' : 'bg-purple-600'
                    }`}>
                      {selectedThreat.risk.toUpperCase()} RISK
                    </span>
                  </div>
                  <h3 className="text-xl font-bold">{selectedThreat.value}</h3>
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" className="text-white border-white hover:bg-white/20 rounded-sm text-xs h-8">
                    Full Report
                  </Button>
                  <Button variant="outline" className="text-white border-white hover:bg-white/20 rounded-sm text-xs h-8">
                    Export
                  </Button>
                </div>
              </div>
            </div>

            {/* Tabs */}
            <div className="border-b border-slate-200">
              <div className="flex">
                <button
                  className={`px-4 py-3 text-sm font-medium ${
                    activeTab === 'overview'
                      ? 'text-[#d82a21] border-b-2 border-[#d82a21]'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                  onClick={() => setActiveTab('overview')}
                >
                  Overview
                </button>
                <button
                  className={`px-4 py-3 text-sm font-medium ${
                    activeTab === 'details'
                      ? 'text-[#d82a21] border-b-2 border-[#d82a21]'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                  onClick={() => setActiveTab('details')}
                >
                  Details
                </button>
                <button
                  className={`px-4 py-3 text-sm font-medium ${
                    activeTab === 'related'
                      ? 'text-[#d82a21] border-b-2 border-[#d82a21]'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                  onClick={() => setActiveTab('related')}
                >
                  Related IOCs
                </button>
              </div>
            </div>

            {/* Tab Content */}
            <div className="p-6">
              {/* Overview Tab */}
              {activeTab === 'overview' && (
                <div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <Card className="p-4 border-l-4 border-[#d82a21]">
                      <h4 className="text-sm text-gray-500 mb-1">First Seen</h4>
                      <p className="font-medium">{selectedThreat.firstSeen}</p>
                    </Card>
                    <Card className="p-4 border-l-4 border-[#d82a21]">
                      <h4 className="text-sm text-gray-500 mb-1">Last Seen</h4>
                      <p className="font-medium">{selectedThreat.lastSeen}</p>
                    </Card>
                    <Card className="p-4 border-l-4 border-[#d82a21]">
                      <h4 className="text-sm text-gray-500 mb-1">
                        {selectedThreat.type === "IP" ? "Location" :
                         selectedThreat.type === "Domain" ? "Registrar" :
                         selectedThreat.type === "File Hash" ? "File Type" : "Status"}
                      </h4>
                      <p className="font-medium">
                        {selectedThreat.type === "IP" ? selectedThreat.location :
                         selectedThreat.type === "Domain" ? selectedThreat.registrar :
                         selectedThreat.type === "File Hash" ? selectedThreat.fileType : selectedThreat.status}
                      </p>
                    </Card>
                  </div>

                  <div className="mb-6">
                    <h4 className="text-sm text-gray-500 mb-2">Description</h4>
                    <p className="text-gray-800">{selectedThreat.description}</p>
                  </div>

                  <div>
                    <h4 className="text-sm text-gray-500 mb-2">Tags</h4>
                    <div className="flex flex-wrap gap-2">
                      {selectedThreat.tags.map((tag, index) => (
                        <span
                          key={index}
                          className="bg-slate-100 text-slate-800 px-2 py-1 rounded text-xs font-medium"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* Details Tab */}
              {activeTab === 'details' && (
                <div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 mb-3">Threat Score</h4>
                      <div className="flex items-center gap-3 mb-6">
                        <div className="w-16 h-16 rounded-full bg-red-100 flex items-center justify-center text-red-800 font-bold text-2xl border-4 border-red-200">
                          87
                        </div>
                        <div>
                          <p className="font-medium">High Confidence</p>
                          <p className="text-sm text-gray-500">Based on 27 indicators</p>
                        </div>
                      </div>

                      <h4 className="text-sm font-medium text-gray-500 mb-3">Behavior Analysis</h4>
                      <ul className="space-y-2 mb-6">
                        <li className="flex items-start gap-2">
                          <span className="bg-red-100 text-red-800 p-1 rounded-full">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <circle cx="12" cy="12" r="10" />
                              <line x1="12" y1="8" x2="12" y2="16" />
                              <line x1="8" y1="12" x2="16" y2="12" />
                            </svg>
                          </span>
                          <span>Command & Control Communication</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <span className="bg-red-100 text-red-800 p-1 rounded-full">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <circle cx="12" cy="12" r="10" />
                              <line x1="12" y1="8" x2="12" y2="16" />
                              <line x1="8" y1="12" x2="16" y2="12" />
                            </svg>
                          </span>
                          <span>Data Exfiltration Attempts</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <span className="bg-red-100 text-red-800 p-1 rounded-full">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <circle cx="12" cy="12" r="10" />
                              <line x1="12" y1="8" x2="12" y2="16" />
                              <line x1="8" y1="12" x2="16" y2="12" />
                            </svg>
                          </span>
                          <span>Evasion Techniques</span>
                        </li>
                      </ul>
                    </div>

                    <div>
                      <h4 className="text-sm font-medium text-gray-500 mb-3">MITRE ATT&CK Tactics</h4>
                      <div className="space-y-3 mb-6">
                        <div className="p-3 bg-slate-50 rounded-md">
                          <div className="flex justify-between mb-1">
                            <span className="text-sm font-medium">Initial Access</span>
                            <span className="text-xs bg-red-100 text-red-800 px-2 rounded">T1566</span>
                          </div>
                          <p className="text-xs text-gray-500">Phishing</p>
                        </div>
                        <div className="p-3 bg-slate-50 rounded-md">
                          <div className="flex justify-between mb-1">
                            <span className="text-sm font-medium">Execution</span>
                            <span className="text-xs bg-red-100 text-red-800 px-2 rounded">T1204</span>
                          </div>
                          <p className="text-xs text-gray-500">User Execution</p>
                        </div>
                        <div className="p-3 bg-slate-50 rounded-md">
                          <div className="flex justify-between mb-1">
                            <span className="text-sm font-medium">Command and Control</span>
                            <span className="text-xs bg-red-100 text-red-800 px-2 rounded">T1071</span>
                          </div>
                          <p className="text-xs text-gray-500">Application Layer Protocol</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Related IOCs Tab */}
              {activeTab === 'related' && (
                <div>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Type
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Value
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Risk Level
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            First Seen
                          </th>
                          <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {selectedThreat.relatedIoCs.map((ioc, index) => (
                          <tr key={index}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {ioc.includes('.') && !ioc.includes('/') ? 'Domain' :
                               /^[0-9.]+$/.test(ioc) ? 'IP' : 'File Hash'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              {ioc}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                High
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              2024-03-15
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <Button variant="ghost" className="h-8 px-2 text-xs text-slate-600 hover:text-[#d82a21]">
                                View Details
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
