/**
 * Subscription Management Hook
 * 
 * Custom hook for managing user subscriptions with Stripe integration
 */

"use client";

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/hooks/useAuth';
import {
  getCurrentSubscription,
  cancelSubscription,
  createCheckoutSession,
  createPortalSession,
  getStripe,
  type Subscription,
  type SubscriptionStatus,
} from '@/lib/stripe/stripe-config';

export interface UseSubscriptionReturn {
  subscription: Subscription | null;
  isLoading: boolean;
  error: string | null;
  isSubscribed: boolean;
  isPro: boolean;
  isEnterprise: boolean;
  refreshSubscription: () => Promise<void>;
  startCheckout: (planId: string, billingCycle: 'monthly' | 'annual') => Promise<void>;
  openPortal: () => Promise<void>;
  cancelSub: () => Promise<void>;
}

/**
 * Hook for managing user subscriptions
 */
export function useSubscription(): UseSubscriptionReturn {
  const { isAuthenticated, user } = useAuth();
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Fetch current subscription
   */
  const refreshSubscription = useCallback(async () => {
    if (!isAuthenticated) {
      setSubscription(null);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const sub = await getCurrentSubscription();
      setSubscription(sub);
    } catch (err) {
      console.error('Failed to fetch subscription:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch subscription');
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated]);

  /**
   * Initialize subscription data on mount and auth changes
   */
  useEffect(() => {
    refreshSubscription();
  }, [refreshSubscription]);

  /**
   * Start Stripe checkout process
   */
  const startCheckout = useCallback(async (planId: string, billingCycle: 'monthly' | 'annual') => {
    if (!isAuthenticated) {
      throw new Error('Must be authenticated to start checkout');
    }

    setIsLoading(true);
    setError(null);

    try {
      // Get the appropriate price ID based on plan and billing cycle
      const priceId = getPriceId(planId, billingCycle);
      if (!priceId) {
        throw new Error(`No price ID found for plan ${planId} with ${billingCycle} billing`);
      }

      // Create checkout session
      const { sessionId } = await createCheckoutSession(priceId);

      // Redirect to Stripe checkout
      const stripe = await getStripe();
      if (!stripe) {
        throw new Error('Failed to load Stripe');
      }

      const { error: stripeError } = await stripe.redirectToCheckout({ sessionId });
      if (stripeError) {
        throw new Error(stripeError.message);
      }
    } catch (err) {
      console.error('Checkout failed:', err);
      setError(err instanceof Error ? err.message : 'Checkout failed');
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated]);

  /**
   * Open Stripe customer portal
   */
  const openPortal = useCallback(async () => {
    if (!isAuthenticated || !subscription) {
      throw new Error('Must have an active subscription to access portal');
    }

    setIsLoading(true);
    setError(null);

    try {
      // Get customer ID from user data or use subscription ID as fallback
      const userWithStripe = user as { stripeCustomerId?: string } | null;
      const customerId = userWithStripe?.stripeCustomerId || subscription.id;
      if (!customerId) {
        throw new Error('No customer ID available');
      }
      const { url } = await createPortalSession(customerId);
      window.location.href = url;
    } catch (err) {
      console.error('Failed to open portal:', err);
      setError(err instanceof Error ? err.message : 'Failed to open portal');
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, subscription, user]);

  /**
   * Cancel subscription
   */
  const cancelSub = useCallback(async () => {
    if (!subscription) {
      throw new Error('No subscription to cancel');
    }

    setIsLoading(true);
    setError(null);

    try {
      await cancelSubscription(subscription.id);
      await refreshSubscription(); // Refresh to get updated status
    } catch (err) {
      console.error('Failed to cancel subscription:', err);
      setError(err instanceof Error ? err.message : 'Failed to cancel subscription');
    } finally {
      setIsLoading(false);
    }
  }, [subscription, refreshSubscription]);

  // Computed properties
  const isSubscribed = subscription?.status === 'active' || subscription?.status === 'trialing';
  const isPro = isSubscribed && subscription?.planId === 'professional';
  const isEnterprise = isSubscribed && subscription?.planId === 'enterprise';

  return {
    subscription,
    isLoading,
    error,
    isSubscribed,
    isPro,
    isEnterprise,
    refreshSubscription,
    startCheckout,
    openPortal,
    cancelSub,
  };
}

/**
 * Get Stripe price ID for a plan and billing cycle
 */
function getPriceId(planId: string, billingCycle: 'monthly' | 'annual'): string | undefined {
  // This should match your Stripe price IDs
  const priceIds: Record<string, Record<string, string>> = {
    professional: {
      monthly: process.env.NEXT_PUBLIC_STRIPE_PROFESSIONAL_MONTHLY_PRICE_ID || '',
      annual: process.env.NEXT_PUBLIC_STRIPE_PROFESSIONAL_ANNUAL_PRICE_ID || '',
    },
    enterprise: {
      monthly: process.env.NEXT_PUBLIC_STRIPE_ENTERPRISE_MONTHLY_PRICE_ID || '',
      annual: process.env.NEXT_PUBLIC_STRIPE_ENTERPRISE_ANNUAL_PRICE_ID || '',
    },
  };

  return priceIds[planId]?.[billingCycle];
}

/**
 * Hook for checking feature access based on subscription
 */
export function useFeatureAccess() {
  const { subscription, isSubscribed, isPro, isEnterprise } = useSubscription();

  const hasFeature = useCallback((feature: string): boolean => {
    // Free tier features
    const freeFeatures = [
      'basic_search',
      'community_api',
      'email_support',
      'basic_reporting',
    ];

    // Professional tier features
    const proFeatures = [
      ...freeFeatures,
      'advanced_search',
      'full_api',
      'priority_support',
      'advanced_analytics',
      'custom_dashboards',
      'threat_hunting',
    ];

    // Enterprise tier features
    const enterpriseFeatures = [
      ...proFeatures,
      'unlimited_search',
      'premium_api',
      'phone_support',
      'custom_integrations',
      'dedicated_manager',
      'sla_guarantees',
    ];

    if (isEnterprise) {
      return enterpriseFeatures.includes(feature);
    }

    if (isPro) {
      return proFeatures.includes(feature);
    }

    return freeFeatures.includes(feature);
  }, [isPro, isEnterprise]);

  const getUsageLimit = useCallback((resource: string): number => {
    const limits: Record<string, Record<string, number>> = {
      searches_per_day: {
        free: 10,
        professional: 1000,
        enterprise: -1, // unlimited
      },
      api_calls_per_month: {
        free: 1000,
        professional: 50000,
        enterprise: -1, // unlimited
      },
    };

    const tier = isEnterprise ? 'enterprise' : isPro ? 'professional' : 'free';
    return limits[resource]?.[tier] || 0;
  }, [isPro, isEnterprise]);

  return {
    hasFeature,
    getUsageLimit,
    subscription,
    isSubscribed,
    isPro,
    isEnterprise,
  };
}
