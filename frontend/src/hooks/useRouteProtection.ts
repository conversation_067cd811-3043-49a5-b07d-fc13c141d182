/**
 * Route Protection Hook
 * 
 * Provides client-side route protection functionality for protected pages.
 * This replaces middleware-based protection for static export compatibility.
 */

"use client";

import React, { useEffect, useState, useMemo } from "react";
import { useRouter, usePathname } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";

// Configuration for route protection
interface RouteProtectionConfig {
  redirectTo?: string;
  requireAuth?: boolean;
  allowedRoles?: string[];
  onUnauthorized?: () => void;
}

// Default configuration
const DEFAULT_CONFIG: RouteProtectionConfig = {
  redirectTo: "/signin",
  requireAuth: true,
};

// Public routes that don't require authentication
const PUBLIC_ROUTES = [
  "/",
  "/signin",
  "/signup",
  "/auth/callback",
  "/blog",
  "/about",
  "/contactus",
  "/api",
  "/api/documentation",
];

/**
 * Check if a route is public (doesn't require authentication)
 */
function isPublicRoute(pathname: string): boolean {
  return PUBLIC_ROUTES.some(route => {
    if (route.endsWith("*")) {
      const prefix = route.slice(0, -1);
      return pathname.startsWith(prefix);
    }
    return pathname === route || pathname.startsWith(`${route}/`);
  });
}

/**
 * Hook for protecting routes that require authentication
 */
export function useRouteProtection(config: RouteProtectionConfig = {}) {
  const router = useRouter();
  const pathname = usePathname();
  const { isAuthenticated, isLoading, user } = useAuth();
  
  const [isAuthorized, setIsAuthorized] = useState<boolean>(false);
  const [isChecking, setIsChecking] = useState<boolean>(true);

  const finalConfig = useMemo(() => ({ ...DEFAULT_CONFIG, ...config }), [config]);

  useEffect(() => {
    // Don't check while auth is still loading
    if (isLoading) {
      setIsChecking(true);
      return;
    }

    // Check if current route is public
    if (isPublicRoute(pathname)) {
      setIsAuthorized(true);
      setIsChecking(false);
      return;
    }

    // Check authentication requirement
    if (finalConfig.requireAuth && !isAuthenticated) {
      setIsAuthorized(false);
      setIsChecking(false);
      
      // Call custom unauthorized handler if provided
      if (finalConfig.onUnauthorized) {
        finalConfig.onUnauthorized();
      } else {
        // Redirect to sign in with return URL
        const redirectUrl = `${finalConfig.redirectTo}?redirect=${encodeURIComponent(pathname)}`;
        router.push(redirectUrl);
      }
      return;
    }

    // Check role-based authorization if roles are specified
    if (finalConfig.allowedRoles && finalConfig.allowedRoles.length > 0) {
      // For now, we'll assume all authenticated users have access
      // This can be extended to check user roles from the user object
      const hasRequiredRole = isAuthenticated; // Simplified for now
      
      if (!hasRequiredRole) {
        setIsAuthorized(false);
        setIsChecking(false);
        
        if (finalConfig.onUnauthorized) {
          finalConfig.onUnauthorized();
        } else {
          router.push("/unauthorized");
        }
        return;
      }
    }

    // User is authorized
    setIsAuthorized(true);
    setIsChecking(false);
  }, [
    isAuthenticated,
    isLoading,
    user,
    pathname,
    router,
    finalConfig,
  ]);

  return {
    isAuthorized,
    isChecking: isChecking || isLoading,
    isAuthenticated,
    user,
  };
}

/**
 * Hook for redirecting authenticated users away from auth pages
 */
export function useAuthRedirect(redirectTo: string = "/") {
  const router = useRouter();
  const pathname = usePathname();
  const { isAuthenticated, isLoading } = useAuth();

  useEffect(() => {
    // Don't redirect while loading
    if (isLoading) return;

    // Redirect authenticated users away from auth pages
    const authPages = ["/signin", "/signup"];
    if (isAuthenticated && authPages.includes(pathname)) {
      router.push(redirectTo);
    }
  }, [isAuthenticated, isLoading, pathname, router, redirectTo]);

  return {
    isAuthenticated,
    isLoading,
  };
}

/**
 * Higher-order component for route protection
 */
export function withRouteProtection<P extends object>(
  Component: React.ComponentType<P>,
  config: RouteProtectionConfig = {}
) {
  return function ProtectedComponent(props: P) {
    const { isAuthorized, isChecking } = useRouteProtection(config);

    // Show loading state while checking authorization
    if (isChecking) {
      return React.createElement(
        'div',
        { className: 'flex items-center justify-center min-h-screen' },
        React.createElement('div', {
          className: 'animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600'
        })
      );
    }

    // Don't render component if not authorized
    if (!isAuthorized) {
      return null;
    }

    // Render the protected component
    return React.createElement(Component, props);
  };
}

/**
 * Component for protecting routes declaratively
 */
interface ProtectedRouteProps {
  children: React.ReactNode;
  config?: RouteProtectionConfig;
  fallback?: React.ReactNode;
}

export function ProtectedRoute({ 
  children, 
  config = {}, 
  fallback 
}: ProtectedRouteProps) {
  const { isAuthorized, isChecking } = useRouteProtection(config);

  // Show loading state while checking
  if (isChecking) {
    return (
      fallback || React.createElement(
        'div',
        { className: 'flex items-center justify-center min-h-screen' },
        React.createElement('div', {
          className: 'animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600'
        })
      )
    );
  }

  // Don't render children if not authorized
  if (!isAuthorized) {
    return null;
  }

  return React.createElement(React.Fragment, null, children);
}
