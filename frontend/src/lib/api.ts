import { fetchAuthSession } from "aws-amplify/auth";

// API基础URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_ENDPOINT || "https://api.example.com";

// 获取认证头信息
async function getAuthHeaders() {
  try {
    // 获取当前认证会话
    const session = await fetchAuthSession();
    
    // 如果有访问令牌，添加到请求头
    if (session.tokens?.accessToken) {
      return {
        Authorization: `Bearer ${session.tokens.accessToken}`,
      };
    }
    
    return {};
  } catch (error) {
    console.error("Failed to get auth headers:", error);
    return {};
  }
}

// 通用API请求函数
export async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  try {
    // 获取认证头信息
    const authHeaders = await getAuthHeaders();
    
    // 构建完整URL
    const url = `${API_BASE_URL}${endpoint}`;
    
    // 合并请求选项
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
      ...(authHeaders.Authorization ? { Authorization: authHeaders.Authorization } : {}),
      ...(options.headers as Record<string, string>),
    };

    const requestOptions: RequestInit = {
      ...options,
      headers,
    };
    
    // 发送请求
    const response = await fetch(url, requestOptions);
    
    // 检查响应状态
    if (!response.ok) {
      // 如果是认证错误，可以触发刷新token或重定向到登录页面
      if (response.status === 401) {
        // 可以在这里添加重定向到登录页面的逻辑
        window.location.href = "/signin?redirect=" + encodeURIComponent(window.location.pathname);
        throw new Error("Authentication failed");
      }
      
      throw new Error(`API request failed: ${response.statusText}`);
    }
    
    // 解析响应数据
    const data = await response.json();
    return data as T;
  } catch (error) {
    console.error(`API request error for ${endpoint}:`, error);
    throw error;
  }
}

// GET请求
export async function get<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
  return apiRequest<T>(endpoint, { ...options, method: "GET" });
}

// POST请求
export async function post<T>(
  endpoint: string,
  data: unknown,
  options: RequestInit = {}
): Promise<T> {
  return apiRequest<T>(endpoint, {
    ...options,
    method: "POST",
    body: JSON.stringify(data),
  });
}

// PUT请求
export async function put<T>(
  endpoint: string,
  data: unknown,
  options: RequestInit = {}
): Promise<T> {
  return apiRequest<T>(endpoint, {
    ...options,
    method: "PUT",
    body: JSON.stringify(data),
  });
}

// DELETE请求
export async function del<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
  return apiRequest<T>(endpoint, { ...options, method: "DELETE" });
}