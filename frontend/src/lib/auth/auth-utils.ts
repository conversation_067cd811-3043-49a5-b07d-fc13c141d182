/**
 * Authentication Utility Functions
 * 
 * This module provides utility functions for authentication operations
 * including sign in, sign out, and user management.
 */

import { signInWithRedirect, signOut, getCurrentUser } from "aws-amplify/auth";
import { TokenManager, type UserInfo } from "./token-manager";

/**
 * Authentication error types
 */
export enum AuthErrorType {
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  UNAUTHORIZED = 'UNAUTHORIZED',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

export interface AuthError {
  type: AuthErrorType;
  message: string;
  originalError?: Error | unknown;
}

/**
 * Create standardized auth error
 */
export function createAuthError(
  type: AuthErrorType,
  message: string,
  originalError?: Error | unknown
): AuthError {
  return {
    type,
    message,
    originalError,
  };
}

/**
 * Redirect to AWS Cognito hosted UI for sign in
 */
export async function redirectToSignIn(): Promise<void> {
  try {
    console.log("🔄 Redirecting to sign in...");
    await signInWithRedirect();
  } catch (error) {
    console.error("❌ Sign in redirect failed:", error);
    throw createAuthError(
      AuthErrorType.CONFIGURATION_ERROR,
      "Failed to redirect to sign in page. Please check your AWS Cognito configuration.",
      error
    );
  }
}

/**
 * Redirect to AWS Cognito hosted UI for sign up
 */
export async function redirectToSignUp(): Promise<void> {
  try {
    console.log("🔄 Redirecting to sign up...");
    // For Cognito hosted UI, sign up is typically handled on the same page as sign in
    await signInWithRedirect();
  } catch (error) {
    console.error("❌ Sign up redirect failed:", error);
    throw createAuthError(
      AuthErrorType.CONFIGURATION_ERROR,
      "Failed to redirect to sign up page. Please check your AWS Cognito configuration.",
      error
    );
  }
}

/**
 * Sign out user and clear all tokens
 */
export async function signOutUser(): Promise<void> {
  try {
    console.log("🔄 Signing out user...");

    // Sign out from Cognito
    await signOut();

    // Clear local tokens
    TokenManager.clearTokens();

    console.log("✅ User signed out successfully");
  } catch (error) {
    console.error("❌ Sign out failed:", error);
    
    // Even if Cognito sign out fails, clear local tokens
    TokenManager.clearTokens();
    
    throw createAuthError(
      AuthErrorType.NETWORK_ERROR,
      "Sign out encountered an issue, but local session has been cleared.",
      error
    );
  }
}

/**
 * Get current authenticated user information
 */
export async function getCurrentUserInfo(): Promise<UserInfo | null> {
  try {
    // First try to get user from stored tokens
    const storedUser = TokenManager.getStoredUserInfo();
    if (storedUser) {
      return storedUser;
    }

    // If no stored user, try to get from Cognito
    const currentUser = await getCurrentUser();
    if (currentUser) {
      // Extract user info from current user object
      const userInfo: UserInfo = {
        sub: currentUser.userId,
        email: currentUser.signInDetails?.loginId || '',
        name: currentUser.username || currentUser.signInDetails?.loginId || 'User',
      };

      return userInfo;
    }

    return null;
  } catch (error) {
    console.warn("⚠️ Failed to get current user info:", error);
    return null;
  }
}

/**
 * Check if user is currently authenticated
 */
export async function isUserAuthenticated(): Promise<boolean> {
  try {
    // Check if we have valid tokens
    if (!TokenManager.areTokensExpired()) {
      const tokens = TokenManager.getStoredTokens();
      if (tokens) {
        return true;
      }
    }

    // Try to refresh tokens
    const refreshedTokens = await TokenManager.refreshTokens();
    return refreshedTokens !== null;
  } catch (error) {
    console.warn("⚠️ Authentication check failed:", error);
    return false;
  }
}

/**
 * Format user display name
 */
export function formatUserDisplayName(user: UserInfo | null): string {
  if (!user) return 'User';
  
  if (user.name && user.name !== user.email) {
    return user.name;
  }
  
  if (user.email) {
    // Extract name from email if no display name
    const emailName = user.email.split('@')[0];
    return emailName.charAt(0).toUpperCase() + emailName.slice(1);
  }
  
  return 'User';
}

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Get authentication status with detailed information
 */
export async function getAuthStatus(): Promise<{
  isAuthenticated: boolean;
  user: UserInfo | null;
  tokensValid: boolean;
  error?: AuthError;
}> {
  try {
    const tokensValid = !TokenManager.areTokensExpired();
    const user = await getCurrentUserInfo();
    const isAuthenticated = await isUserAuthenticated();

    return {
      isAuthenticated,
      user,
      tokensValid,
    };
  } catch (error) {
    return {
      isAuthenticated: false,
      user: null,
      tokensValid: false,
      error: createAuthError(
        AuthErrorType.UNKNOWN_ERROR,
        "Failed to determine authentication status",
        error
      ),
    };
  }
}

/**
 * Handle authentication callback after OAuth redirect
 */
export async function handleAuthCallback(): Promise<{
  success: boolean;
  user?: UserInfo;
  error?: AuthError;
}> {
  try {
    console.log("🔄 Handling authentication callback...");

    // Get fresh tokens from the session
    const tokens = await TokenManager.refreshTokens(true);
    
    if (!tokens) {
      throw new Error("No tokens received after authentication");
    }

    // Get user information
    const user = await getCurrentUserInfo();
    
    if (!user) {
      throw new Error("Failed to retrieve user information");
    }

    console.log("✅ Authentication callback handled successfully");
    
    return {
      success: true,
      user,
    };
  } catch (error) {
    console.error("❌ Authentication callback failed:", error);
    
    // Clear any partial tokens
    TokenManager.clearTokens();
    
    return {
      success: false,
      error: createAuthError(
        AuthErrorType.UNAUTHORIZED,
        "Authentication failed. Please try signing in again.",
        error
      ),
    };
  }
}
