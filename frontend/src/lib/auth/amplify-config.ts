/**
 * AWS Amplify Configuration for Static Export
 * 
 * This module handles the initialization of AWS Amplify for client-side authentication
 * compatible with Next.js static export and S3 deployment.
 */

import { Amplify } from "aws-amplify";
import { cognitoUserPoolsTokenProvider } from "aws-amplify/auth/cognito";

// Environment variables interface for type safety
interface CognitoConfig {
  userPoolId: string;
  userPoolClientId: string;
  cognitoDomain: string;
  redirectSignIn: string[];
  redirectSignOut: string[];
  apiEndpoint?: string;
  s3Bucket?: string;
  s3Region?: string;
}

// Global flag to track initialization
let isAmplifyConfigured = false;

/**
 * Get Cognito configuration from environment variables
 */
function getCognitoConfig(): CognitoConfig {
  const userPoolId = process.env.NEXT_PUBLIC_COGNITO_USER_POOL_ID;
  const userPoolClientId = process.env.NEXT_PUBLIC_COGNITO_CLIENT_ID;
  const cognitoDomain = process.env.NEXT_PUBLIC_COGNITO_DOMAIN;
  const redirectSignIn = process.env.NEXT_PUBLIC_REDIRECT_SIGN_IN?.split(",") || [];
  const redirectSignOut = process.env.NEXT_PUBLIC_REDIRECT_SIGN_OUT?.split(",") || [];
  const apiEndpoint = process.env.NEXT_PUBLIC_API_ENDPOINT;
  const s3Bucket = process.env.NEXT_PUBLIC_S3_BUCKET;
  const s3Region = process.env.NEXT_PUBLIC_S3_REGION;

  // Validate required environment variables
  if (!userPoolId || !userPoolClientId || !cognitoDomain) {
    throw new Error(
      "Missing required AWS Cognito configuration. Please check your environment variables:\n" +
      "- NEXT_PUBLIC_COGNITO_USER_POOL_ID\n" +
      "- NEXT_PUBLIC_COGNITO_CLIENT_ID\n" +
      "- NEXT_PUBLIC_COGNITO_DOMAIN"
    );
  }

  return {
    userPoolId,
    userPoolClientId,
    cognitoDomain,
    redirectSignIn,
    redirectSignOut,
    apiEndpoint,
    s3Bucket,
    s3Region,
  };
}

/**
 * Configure secure token storage for browser environment
 */
function configureTokenStorage() {
  cognitoUserPoolsTokenProvider.setKeyValueStorage({
    getItem: async (key: string): Promise<string | null> => {
      try {
        if (typeof window === 'undefined') return null;
        return localStorage.getItem(key);
      } catch (error) {
        console.warn(`Failed to get item from storage: ${key}`, error);
        return null;
      }
    },
    setItem: async (key: string, value: string): Promise<void> => {
      try {
        if (typeof window === 'undefined') return;
        localStorage.setItem(key, value);
      } catch (error) {
        console.error(`Failed to set item in storage: ${key}`, error);
        throw error;
      }
    },
    removeItem: async (key: string): Promise<void> => {
      try {
        if (typeof window === 'undefined') return;
        localStorage.removeItem(key);
      } catch (error) {
        console.warn(`Failed to remove item from storage: ${key}`, error);
      }
    },
    clear: async (): Promise<void> => {
      try {
        if (typeof window === 'undefined') return;
        localStorage.clear();
      } catch (error) {
        console.warn('Failed to clear storage', error);
      }
    },
  });
}

/**
 * Initialize AWS Amplify configuration
 * This function is safe to call multiple times
 */
export async function initializeAmplify(): Promise<void> {
  // Prevent multiple initializations
  if (isAmplifyConfigured) {
    return;
  }

  try {
    const config = getCognitoConfig();

    // Configure Amplify
    Amplify.configure({
      Auth: {
        Cognito: {
          userPoolId: config.userPoolId,
          userPoolClientId: config.userPoolClientId,
          loginWith: {
            oauth: {
              domain: config.cognitoDomain,
              scopes: ["openid", "email", "profile"],
              redirectSignIn: config.redirectSignIn,
              redirectSignOut: config.redirectSignOut,
              responseType: "code",
            },
          },
        },
      },
      ...(config.apiEndpoint && {
        API: {
          REST: {
            gcandle: {
              endpoint: config.apiEndpoint,
              region: "us-east-1",
            },
          },
        },
      }),
      ...(config.s3Bucket && {
        Storage: {
          S3: {
            bucket: config.s3Bucket,
            region: config.s3Region || "us-east-1",
          },
        },
      }),
    });

    // Configure secure token storage
    configureTokenStorage();

    isAmplifyConfigured = true;
    console.log("✅ AWS Amplify configured successfully for static export");
  } catch (error) {
    console.error("❌ Failed to configure AWS Amplify:", error);
    throw error;
  }
}

/**
 * Check if Amplify is configured
 */
export function isAmplifyInitialized(): boolean {
  return isAmplifyConfigured;
}

/**
 * Reset Amplify configuration (useful for testing)
 */
export function resetAmplifyConfig(): void {
  isAmplifyConfigured = false;
}
