/**
 * Secure Token Management for AWS Cognito
 * 
 * This module handles secure storage, validation, and refresh of JWT tokens
 * following SaaS best practices for client-side authentication.
 */

import { fetchAuthSession } from "aws-amplify/auth";
import { jwtDecode } from "jwt-decode";

// Token storage keys
const TOKEN_KEYS = {
  ACCESS_TOKEN: 'gcandle_access_token',
  ID_TOKEN: 'gcandle_id_token',
  REFRESH_TOKEN: 'gcandle_refresh_token',
  TOKEN_EXPIRATION: 'gcandle_token_expiration',
  USER_INFO: 'gcandle_user_info',
} as const;

// Token interfaces
export interface TokenInfo {
  accessToken: string;
  idToken: string;
  refreshToken: string;
  expiresAt: number;
}

export interface UserInfo {
  sub: string;
  email: string;
  name?: string;
  email_verified?: boolean;
  [key: string]: unknown;
}

export interface DecodedToken {
  sub: string;
  email?: string;
  name?: string;
  exp: number;
  iat: number;
  email_verified?: boolean;
  [key: string]: unknown;
}

/**
 * Secure token storage utilities
 */
export class TokenManager {
  /**
   * Store tokens securely in localStorage
   */
  static storeTokens(tokens: TokenInfo): void {
    try {
      if (typeof window === 'undefined') return;

      localStorage.setItem(TOKEN_KEYS.ACCESS_TOKEN, tokens.accessToken);
      localStorage.setItem(TOKEN_KEYS.ID_TOKEN, tokens.idToken);
      localStorage.setItem(TOKEN_KEYS.REFRESH_TOKEN, tokens.refreshToken);
      localStorage.setItem(TOKEN_KEYS.TOKEN_EXPIRATION, tokens.expiresAt.toString());

      // Store user info from ID token
      const userInfo = this.extractUserInfoFromToken(tokens.idToken);
      if (userInfo) {
        localStorage.setItem(TOKEN_KEYS.USER_INFO, JSON.stringify(userInfo));
      }

      console.log("✅ Tokens stored securely");
    } catch (error) {
      console.error("❌ Failed to store tokens:", error);
      throw new Error("Failed to store authentication tokens");
    }
  }

  /**
   * Retrieve stored tokens
   */
  static getStoredTokens(): TokenInfo | null {
    try {
      if (typeof window === 'undefined') return null;

      const accessToken = localStorage.getItem(TOKEN_KEYS.ACCESS_TOKEN);
      const idToken = localStorage.getItem(TOKEN_KEYS.ID_TOKEN);
      const refreshToken = localStorage.getItem(TOKEN_KEYS.REFRESH_TOKEN);
      const expirationStr = localStorage.getItem(TOKEN_KEYS.TOKEN_EXPIRATION);

      if (!accessToken || !idToken || !refreshToken || !expirationStr) {
        return null;
      }

      return {
        accessToken,
        idToken,
        refreshToken,
        expiresAt: parseInt(expirationStr, 10),
      };
    } catch (error) {
      console.warn("⚠️ Failed to retrieve stored tokens:", error);
      return null;
    }
  }

  /**
   * Get stored user information
   */
  static getStoredUserInfo(): UserInfo | null {
    try {
      if (typeof window === 'undefined') return null;

      const userInfoStr = localStorage.getItem(TOKEN_KEYS.USER_INFO);
      if (!userInfoStr) return null;

      return JSON.parse(userInfoStr);
    } catch (error) {
      console.warn("⚠️ Failed to retrieve user info:", error);
      return null;
    }
  }

  /**
   * Clear all stored tokens and user data
   */
  static clearTokens(): void {
    try {
      if (typeof window === 'undefined') return;

      Object.values(TOKEN_KEYS).forEach(key => {
        localStorage.removeItem(key);
      });

      console.log("✅ Tokens cleared successfully");
    } catch (error) {
      console.warn("⚠️ Failed to clear tokens:", error);
    }
  }

  /**
   * Check if tokens are expired
   */
  static areTokensExpired(): boolean {
    const tokens = this.getStoredTokens();
    if (!tokens) return true;

    const now = Date.now();
    const bufferTime = 5 * 60 * 1000; // 5 minutes buffer

    return now >= (tokens.expiresAt - bufferTime);
  }

  /**
   * Extract user information from ID token
   */
  static extractUserInfoFromToken(idToken: string): UserInfo | null {
    try {
      const decoded = jwtDecode<DecodedToken>(idToken);
      return {
        ...decoded,
        sub: decoded.sub,
        email: decoded.email || '',
        name: decoded.name || decoded.email || 'User',
        email_verified: decoded.email_verified,
      };
    } catch (error) {
      console.error("❌ Failed to decode ID token:", error);
      return null;
    }
  }

  /**
   * Validate token format and structure
   */
  static validateToken(token: string): boolean {
    try {
      const decoded = jwtDecode(token);
      return decoded && typeof decoded === 'object' && 'exp' in decoded;
    } catch {
      return false;
    }
  }

  /**
   * Get fresh tokens from Amplify
   */
  static async refreshTokens(forceRefresh = false): Promise<TokenInfo | null> {
    try {
      console.log("🔄 Refreshing authentication tokens...");

      const session = await fetchAuthSession({ forceRefresh });

      if (!session.tokens) {
        console.warn("⚠️ No tokens received from session");
        return null;
      }

      const { accessToken, idToken } = session.tokens;

      if (!accessToken || !idToken) {
        console.warn("⚠️ Incomplete token set received");
        return null;
      }

      const tokenInfo: TokenInfo = {
        accessToken: accessToken.toString(),
        idToken: idToken.toString(),
        refreshToken: '', // Refresh token is managed by Amplify internally
        expiresAt: (accessToken.payload.exp as number) * 1000,
      };

      // Store the refreshed tokens
      this.storeTokens(tokenInfo);

      console.log("✅ Tokens refreshed successfully");
      return tokenInfo;
    } catch (error) {
      console.error("❌ Failed to refresh tokens:", error);
      return null;
    }
  }

  /**
   * Get current access token (refresh if needed)
   */
  static async getValidAccessToken(): Promise<string | null> {
    try {
      // Check if we need to refresh tokens
      if (this.areTokensExpired()) {
        const refreshedTokens = await this.refreshTokens(true);
        return refreshedTokens?.accessToken || null;
      }

      // Return stored token if still valid
      const storedTokens = this.getStoredTokens();
      return storedTokens?.accessToken || null;
    } catch (error) {
      console.error("❌ Failed to get valid access token:", error);
      return null;
    }
  }
}
