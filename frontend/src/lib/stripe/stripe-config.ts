/**
 * Stripe Configuration for SaaS Billing
 * 
 * This module handles Stripe integration for subscription management
 * compatible with Next.js static export and client-side authentication.
 */

import { loadStripe, Stripe } from '@stripe/stripe-js';

// Stripe configuration interface
interface StripeConfig {
  publishableKey: string;
  apiEndpoint: string;
}

// Global Stripe instance
let stripePromise: Promise<Stripe | null> | null = null;

/**
 * Get Stripe configuration from environment variables
 */
function getStripeConfig(): StripeConfig {
  const publishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;
  const apiEndpoint = process.env.NEXT_PUBLIC_API_ENDPOINT;

  if (!publishableKey) {
    throw new Error('Missing NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY environment variable');
  }

  if (!apiEndpoint) {
    throw new Error('Missing NEXT_PUBLIC_API_ENDPOINT environment variable');
  }

  return {
    publishableKey,
    apiEndpoint,
  };
}

/**
 * Initialize and return Stripe instance
 */
export function getStripe(): Promise<Stripe | null> {
  if (!stripePromise) {
    const config = getStripeConfig();
    stripePromise = loadStripe(config.publishableKey);
  }
  return stripePromise;
}

/**
 * Stripe price IDs for different plans and billing cycles
 */
export const STRIPE_PRICE_IDS = {
  professional: {
    monthly: process.env.NEXT_PUBLIC_STRIPE_PROFESSIONAL_MONTHLY_PRICE_ID,
    annual: process.env.NEXT_PUBLIC_STRIPE_PROFESSIONAL_ANNUAL_PRICE_ID,
  },
  enterprise: {
    monthly: process.env.NEXT_PUBLIC_STRIPE_ENTERPRISE_MONTHLY_PRICE_ID,
    annual: process.env.NEXT_PUBLIC_STRIPE_ENTERPRISE_ANNUAL_PRICE_ID,
  },
} as const;

/**
 * Plan configuration for Stripe integration
 */
export interface StripePlan {
  id: string;
  name: string;
  priceIds: {
    monthly?: string;
    annual?: string;
  };
}

export const STRIPE_PLANS: StripePlan[] = [
  {
    id: 'professional',
    name: 'Professional',
    priceIds: {
      monthly: STRIPE_PRICE_IDS.professional.monthly,
      annual: STRIPE_PRICE_IDS.professional.annual,
    },
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    priceIds: {
      monthly: STRIPE_PRICE_IDS.enterprise.monthly,
      annual: STRIPE_PRICE_IDS.enterprise.annual,
    },
  },
];

/**
 * Create checkout session for subscription
 */
export async function createCheckoutSession(
  priceId: string,
  customerId?: string,
  successUrl?: string,
  cancelUrl?: string
): Promise<{ sessionId: string; url: string }> {
  const config = getStripeConfig();
  
  const response = await fetch(`${config.apiEndpoint}/stripe/create-checkout-session`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${await getAccessToken()}`,
    },
    body: JSON.stringify({
      priceId,
      customerId,
      successUrl: successUrl || `${window.location.origin}/profile?success=true`,
      cancelUrl: cancelUrl || `${window.location.origin}/plan?canceled=true`,
    }),
  });

  if (!response.ok) {
    throw new Error('Failed to create checkout session');
  }

  return response.json();
}

/**
 * Create customer portal session
 */
export async function createPortalSession(customerId: string): Promise<{ url: string }> {
  const config = getStripeConfig();
  
  const response = await fetch(`${config.apiEndpoint}/stripe/create-portal-session`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${await getAccessToken()}`,
    },
    body: JSON.stringify({
      customerId,
      returnUrl: `${window.location.origin}/profile`,
    }),
  });

  if (!response.ok) {
    throw new Error('Failed to create portal session');
  }

  return response.json();
}

/**
 * Get access token for API requests
 */
async function getAccessToken(): Promise<string> {
  // This should integrate with your existing auth system
  // For now, we'll use a placeholder
  const token = localStorage.getItem('gcandle_access_token');
  if (!token) {
    throw new Error('No access token available');
  }
  return token;
}

/**
 * Subscription status types
 */
export type SubscriptionStatus = 
  | 'active'
  | 'canceled'
  | 'incomplete'
  | 'incomplete_expired'
  | 'past_due'
  | 'trialing'
  | 'unpaid';

/**
 * Subscription interface
 */
export interface Subscription {
  id: string;
  status: SubscriptionStatus;
  planId: string;
  planName: string;
  currentPeriodStart: number;
  currentPeriodEnd: number;
  cancelAtPeriodEnd: boolean;
  trialEnd?: number;
}

/**
 * Get user's current subscription
 */
export async function getCurrentSubscription(): Promise<Subscription | null> {
  const config = getStripeConfig();
  
  try {
    const response = await fetch(`${config.apiEndpoint}/stripe/subscription`, {
      headers: {
        'Authorization': `Bearer ${await getAccessToken()}`,
      },
    });

    if (!response.ok) {
      if (response.status === 404) {
        return null; // No subscription found
      }
      throw new Error('Failed to fetch subscription');
    }

    return response.json();
  } catch (error) {
    console.error('Error fetching subscription:', error);
    return null;
  }
}

/**
 * Cancel subscription
 */
export async function cancelSubscription(subscriptionId: string): Promise<void> {
  const config = getStripeConfig();
  
  const response = await fetch(`${config.apiEndpoint}/stripe/subscription/${subscriptionId}/cancel`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${await getAccessToken()}`,
    },
  });

  if (!response.ok) {
    throw new Error('Failed to cancel subscription');
  }
}
