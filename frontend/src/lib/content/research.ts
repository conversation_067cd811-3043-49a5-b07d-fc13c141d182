// Import research data generated at build time
import { researchData } from './research-data';

export interface ResearchArticle {
  slug: string;
  title: string;
  excerpt: string;
  content: string;
  date: string;
  author: string;
  authorRole: string;
  image: string;
  readTime: string;
  category: string;
  tags: string[];
  iocs?: string[];
}

// Research data will be generated at build time

export function getResearchArticles(): ResearchArticle[] {
  try {
    // Use research data generated at build time
    return (researchData as ResearchArticle[]) || [];
  } catch (error) {
    console.error('Error loading research data:', error);
    return [];
  }
}

export function getResearchArticle(slug: string): ResearchArticle | undefined {
  const articles = getResearchArticles();
  return articles.find(article => article.slug === slug);
}

export function getResearchCategories(): string[] {
  const articles = getResearchArticles();
  const categories = ["All Research"];
  const uniqueCategories = [...new Set(articles.map(article => article.category))];
  return categories.concat(uniqueCategories);
}

export function getResearchArticlesByCategory(category: string): ResearchArticle[] {
  const articles = getResearchArticles();
  if (category === "All Research") {
    return articles;
  }
  return articles.filter(article => article.category === category);
}

// Function to generate static params for dynamic routes
export function getAllResearchSlugs(): string[] {
  const articles = getResearchArticles();
  return articles.map(article => article.slug);
}

// Note: Research articles are now read directly from the public/research directory
// This allows for immediate updates without requiring a rebuild

