/**
 * Authentication Context
 * 
 * Provides global authentication state management using React Context.
 * This context is compatible with Next.js static export and client-side rendering.
 */

"use client";

import React, { createContext, useContext, useEffect, useState, useCallback, ReactNode } from "react";
import { useRouter } from "next/navigation";
import { initializeAmplify } from "@/lib/auth/amplify-config";
import { TokenManager, type UserInfo } from "@/lib/auth/token-manager";
import { 
  signOutUser, 
  getCurrentUserInfo, 
  isUserAuthenticated,
  type AuthError,
  AuthErrorType,
  createAuthError 
} from "@/lib/auth/auth-utils";

// Authentication state interface
export interface AuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: UserInfo | null;
  error: AuthError | null;
}

// Authentication context interface
export interface AuthContextType extends AuthState {
  signOut: () => Promise<void>;
  refreshAuth: () => Promise<void>;
  clearError: () => void;
  getAccessToken: () => Promise<string | null>;
}

// Create the authentication context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Authentication provider props
interface AuthProviderProps {
  children: ReactNode;
}

/**
 * Authentication Provider Component
 * 
 * Manages global authentication state and provides auth methods to child components.
 */
export function AuthProvider({ children }: AuthProviderProps) {
  const router = useRouter();
  
  // Authentication state
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    isLoading: true,
    user: null,
    error: null,
  });

  /**
   * Update authentication state
   */
  const updateAuthState = useCallback((updates: Partial<AuthState>) => {
    setAuthState(prev => ({ ...prev, ...updates }));
  }, []);

  /**
   * Clear authentication error
   */
  const clearError = useCallback(() => {
    updateAuthState({ error: null });
  }, [updateAuthState]);

  /**
   * Check and update authentication status
   */
  const checkAuthStatus = useCallback(async () => {
    try {
      updateAuthState({ isLoading: true, error: null });

      // Check if user is authenticated
      const authenticated = await isUserAuthenticated();
      
      if (authenticated) {
        // Get user information
        const user = await getCurrentUserInfo();
        
        updateAuthState({
          isAuthenticated: true,
          user,
          isLoading: false,
          error: null,
        });
      } else {
        updateAuthState({
          isAuthenticated: false,
          user: null,
          isLoading: false,
          error: null,
        });
      }
    } catch (error) {
      console.error("❌ Authentication status check failed:", error);
      
      updateAuthState({
        isAuthenticated: false,
        user: null,
        isLoading: false,
        error: createAuthError(
          AuthErrorType.UNKNOWN_ERROR,
          "Failed to check authentication status",
          error
        ),
      });
    }
  }, [updateAuthState]);

  /**
   * Sign out user
   */
  const signOut = useCallback(async () => {
    try {
      updateAuthState({ isLoading: true, error: null });
      
      await signOutUser();
      
      updateAuthState({
        isAuthenticated: false,
        user: null,
        isLoading: false,
        error: null,
      });

      // Redirect to home page
      router.push("/");
    } catch (error) {
      console.error("❌ Sign out failed:", error);
      
      // Even if sign out fails, update local state
      updateAuthState({
        isAuthenticated: false,
        user: null,
        isLoading: false,
        error: error as AuthError,
      });
      
      // Still redirect to home
      router.push("/");
    }
  }, [updateAuthState, router]);

  /**
   * Refresh authentication state
   */
  const refreshAuth = useCallback(async () => {
    await checkAuthStatus();
  }, [checkAuthStatus]);

  /**
   * Get valid access token
   */
  const getAccessToken = useCallback(async (): Promise<string | null> => {
    try {
      return await TokenManager.getValidAccessToken();
    } catch (error) {
      console.error("❌ Failed to get access token:", error);
      return null;
    }
  }, []);

  /**
   * Initialize authentication on mount
   */
  useEffect(() => {
    let mounted = true;

    const initializeAuth = async () => {
      try {
        // Initialize Amplify configuration
        await initializeAmplify();
        
        // Check initial authentication status
        if (mounted) {
          await checkAuthStatus();
        }
      } catch (error) {
        console.error("❌ Failed to initialize authentication:", error);
        
        if (mounted) {
          updateAuthState({
            isAuthenticated: false,
            user: null,
            isLoading: false,
            error: createAuthError(
              AuthErrorType.CONFIGURATION_ERROR,
              "Failed to initialize authentication system",
              error
            ),
          });
        }
      }
    };

    initializeAuth();

    return () => {
      mounted = false;
    };
  }, [checkAuthStatus, updateAuthState]);

  /**
   * Set up token refresh interval
   */
  useEffect(() => {
    if (!authState.isAuthenticated) return;

    // Check token expiration every 5 minutes
    const tokenCheckInterval = setInterval(async () => {
      if (TokenManager.areTokensExpired()) {
        console.log("🔄 Tokens expired, refreshing...");
        await refreshAuth();
      }
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(tokenCheckInterval);
  }, [authState.isAuthenticated, refreshAuth]);

  // Context value
  const contextValue: AuthContextType = {
    ...authState,
    signOut,
    refreshAuth,
    clearError,
    getAccessToken,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

/**
 * Hook to use authentication context
 * 
 * @throws Error if used outside of AuthProvider
 */
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  
  return context;
}
