# 攻击再升级，DarkHotel组织最新RPC攻击组件披露

## 1.1. **背景**

### 组织介绍

​	DarkHotel组织于2014年被国外安全厂商披露，据悉最早可追溯到2010年。该组织因针对入住高档酒店的商贸高管和国家政要而得名，攻击目标范围涉及中国、朝鲜、日本、缅甸、印度以及少数欧洲国家，被认为是具有朝鲜半岛政府背景的APT组织。近年来，我们观察到其攻击目标早已脱离了Darkhotel名字代表的酒店行业，已然涉及外贸、政府机构、科研单位、军工等行业，是近年来针对周边国家频繁发起攻击的APT组织之一。

### ‌事件概况

​	近期，知道创宇404高级威胁情报团队在狩猎时再次捕获到DarkHotel组织的攻击活动。攻击活动中利用带有微软签名的系统程序，侧载执行恶意Dll文件，最终导致恶意组件的解密和加载。相较于2023年发现的该组织使用的InitPlugins框架，本次攻击活动中对框架进行了升级。

### ‌核心发现

​	DarkHotel在攻击手法和技术上依然展现出较高的水准，在受害者主机开启主动防御及杀毒软件的情况下，仍然能够游刃有余的开展攻击，可见其免杀水准之高。

​	攻击者精通MFC相关的加载机制，拥有极强的编码能力。在代码方面使用了多种混淆和双层注入机制，不仅能够增加分析难度，还能隐藏自身行踪，并且能保证注入代码的稳定运行。

​	在功能设计上，采用了模块化加载的方式，将加密的模块文件伪装成系统文件。为达到以假乱真的目的，更是将伪装文件的文件时间修改为系统文件kernel32.dll（正常情况下系统安装或者系统更新时文件时间会进行修改）的文件时间。

​	攻击者还利用了RPC技术来执行相关的组件功能，并将通信组件与功能组件进行了区分，二者使用不同的计划任务来保持驻留。

## 1.2 技术分析

### 攻击流程

![image-20250411145154520](assets/image-20250411145154520.png)

### 关键技术

**MFC本地资源加载机制**

在MFC中存在一种MFC 本地化资源加载机制，既当MFC编写的exe或者dll（假设为MFCxx.exe）加载资源时，并不会直接加载自身资源，而是会按照以下顺序查找资源dll。

1、当前用户的默认 UI 语言，从 GetUserDefaultUILanguage() Win32 API 返回，例如为FRC(加拿大法语)，则加载MFCxxFRC.dll。

![image-20250410135119646](assets/image-20250410135119646.png)

2、当前用户的默认 UI 语言，没有任何特定的子语言，则加载MFCxxFRA.dll。

3、系统的默认 UI 语言，由 GetSystemDefaultUILanguage() API 返回。在其他平台上，这是操作系统本身的语言，例如系统语言为ENC（加拿大英语），则加载MFCxxENC.dll。

4、系统默认的 UI 语言，没有任何特定的子语言，则加载MFCxxENU.dll。

5、一种带有 3 个字母代码 LOC 的假语言,即加载MFCxxLOC.dll。

![image-20250410135138846](assets/image-20250410135138846.png)

计划任务主程序目录结构如下图，攻击者将LOC.dll释放到主程序同目录下，导致该dll的加载执行：

![image-20250410140425126](assets/image-20250410140425126.png)

**代码混淆**

攻击者使用了多种类型的混淆代码，使得代码量膨胀，极大的增加了分析的难度。混淆代码中的一种如下：

![img](assets/wps1.jpg)  

**双重注入**

第一重注入：通过查找指定系统进程，并将创建进程相关的代码进行注入，以此获取较高的权限，避免注入失败：

![img](assets/wps3.jpg) 

第二重注入：将恶意载荷注入到第一重注入中启动的系统进程中：

![img](assets/wps4.jpg) 

**系统文件伪装**

​	攻击者内置了一个待解密的文件列表，文件内容均为加密密文。从列表中的文件路径和文件名不难看出，攻击者有意将相关文件伪装成系统密钥文件。为了增加文件可信度，通过读取当前主机的kernel32.dll的文件时间，并将文件列表中所有存在的.pem文件的文件时间与kernel32.dll对齐。

![image-20250410140554012](assets/image-20250410140554012.png)



**本地RPC调用机制**

功能组件注册RPC接口：

![image-20250411103553450](assets/image-20250411103553450.png)

核心加载组件调用接口：

![image-20250411103915253](assets/image-20250411103915253.png)

### 组件分析

**核心加载组件**

攻击者在核心加载组件中设计了多种类型的组件加载方式，根据攻击者设置的type值可分为：

type1：创建线程执行shellcode：

![image-20250411105420360](assets/image-20250411105420360.png)

type2：反射加载：

![image-20250411105731119](assets/image-20250411105731119.png)

type3：注入执行：

![image-20250411110549996](assets/image-20250411110549996.png)

type4：使用LoadLibraryW加载：

![image-20250411110748871](assets/image-20250411110748871.png)

其中前3种都需要使用相同的算法和key进行解密：

![image-20250411111006586](assets/image-20250411111006586.png)

**功能组件**

共捕获到4个组件，分为两种类型，具体见下表：

![image-20250411143503041](assets/image-20250411143503041.png)

### **归因**

**算法关联**

DarkHotel此前曾曝光的攻击事件中，曾多次使用的异或算法进行进行加解密，例如在2023年该组织使用initplugins架构的攻击活动中使用的算法：

![image-20250411142210095](assets/image-20250411142210095.png)

**架构关联**

本次DarkHote组织使用的组件加载框架与2023年捕获的initplugins框架均通过读取内置的文件列表并解密加载：

![image-20250411143814970](assets/image-20250411143814970.png)

**组件关联**

​	本次捕获的攻击活动中加载的组件与2023年initplugins攻击活动中使用的功能组件如出一辙。

![image-20250411125528231](assets/image-20250411125528231.png)

​	基于以上关联，我们有极大的信心认为本次捕获的样本系DarkHotel组织所为。相较于之前该组织的攻击活动，本次DarkHotel将远控组件（meterpreter）与功能组件（键盘记录、屏幕截图和USB窃密）分开加载运行，此举可能是为防止单一组件被发现后导致整体攻击链路的失效，可见该组织一致在致力于提升武器的健壮性，增加攻击成功几率。

## 1.4 IOC



​	e14459863ac38df87e59e0a93a0fa438	

​	d61308ddf2624e726fe83cd487dd6fe3

​	c92afb4d4fc5effd49cbc048d4355d1c

​	7cff54d8227393c352ee0859bc5cf01e