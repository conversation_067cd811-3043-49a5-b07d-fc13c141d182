# Attack Upgraded: Disclosure of DarkHotel Organization's Latest RPC Attack Components

## 1.1. **Background**

### Organization Introduction

The DarkHotel organization was disclosed by foreign security vendors in 2014, reportedly dating back to 2010. The group got its name from targeting business executives and state dignitaries staying in luxury hotels. Their attack targets range across China, North Korea, Japan, Myanmar, India, and a few European countries, and they are considered an APT group with a Korean Peninsula government background. In recent years, we have observed that their attack targets have moved beyond the hotel industry represented by the DarkHotel name, now including foreign trade, government agencies, research institutions, military industries, and other sectors, making them one of the APT groups that frequently launch attacks against neighboring countries in recent years.

### Event Overview

Recently, the Knownsec 404 Advanced Threat Intelligence Team captured another attack activity from the DarkHotel organization during hunting. The attack activity utilized Microsoft-signed system programs to side-load malicious DLL files, ultimately leading to the decryption and loading of malicious components. Compared to the InitPlugins framework used by the organization discovered in 2023, the framework has been upgraded in this attack activity.

### Core Findings

DarkHotel continues to demonstrate a high standard in attack methods and techniques. Even with active defense and antivirus software running on victim hosts, they can still carry out attacks effortlessly, showing their high level of anti-detection capabilities.

The attackers are proficient in MFC-related loading mechanisms and possess extremely strong coding abilities. They used various obfuscation techniques and a dual-layer injection mechanism in their code, which not only increases the difficulty of analysis but also hides their tracks while ensuring the stable operation of the injected code.

In terms of functional design, they adopted a modular loading approach, disguising encrypted module files as system files. To achieve convincing deception, they even modified the timestamp of the disguised files to match the timestamp of the system file kernel32.dll (normally, file times are modified during system installation or system updates).

The attackers also utilized RPC technology to execute component functions, separating communication components from functional components, with the two using different scheduled tasks to maintain persistence.

## 1.2 Technical Analysis

### Attack Flow

![image-20250411152420501](assets/image-20250411152420501.png)

### Key Technologies

**MFC Local Resource Loading Mechanism**

There is a localization resource loading mechanism in MFC, where when an MFC-written exe or dll (let's call it MFCxx.exe) loads resources, it doesn't directly load its own resources but looks for resource dlls in the following order:

1. The current user's default UI language, returned from the GetUserDefaultUILanguage() Win32 API. For example, if it's FRC (Canadian French), it loads MFCxxFRC.dll.

![image-20250410135119646](assets/image-20250410135119646.png)

2. The current user's default UI language without any specific sublanguage, then it loads MFCxxFRA.dll.

3. The system's default UI language, returned by the GetSystemDefaultUILanguage() API. On other platforms, this is the language of the operating system itself. For example, if the system language is ENC (Canadian English), it loads MFCxxENC.dll.

4. The system's default UI language without any specific sublanguage, then it loads MFCxxENU.dll.

5. A fake language with a 3-letter code LOC, thus loading MFCxxLOC.dll.

![image-20250410135138846](assets/image-20250410135138846.png)

The directory structure of the scheduled task's main program is as shown below. The attacker released LOC.dll to the main program's directory, causing this dll to be loaded and executed:

![image-20250410140425126](assets/image-20250410140425126.png)

**Code Obfuscation**

The attackers used multiple types of obfuscated code, causing the code to bloat and greatly increasing the difficulty of analysis. One of the obfuscated codes is as follows:

![img](assets/wps1.jpg)  

**Double Injection**

First injection: By searching for specified system processes and injecting code related to process creation, they obtain higher privileges and avoid injection failure:

![img](assets/wps3.jpg) 

Second injection: Injecting the malicious payload into the system process started by the first injection:

![img](assets/wps4.jpg) 

**System File Disguise**

The attacker embedded a list of files to be decrypted, all of which were encrypted ciphertexts. From the file paths and filenames in the list, it's clear that the attacker intentionally disguised the related files as system key files. To increase file credibility, they read the file timestamp of kernel32.dll on the current host and aligned the timestamps of all existing .pem files in the file list with that of kernel32.dll.

![image-20250410140554012](assets/image-20250410140554012.png)

**Local RPC Call Mechanism**

Functional component registering RPC interface:

![image-20250411103553450](assets/image-20250411103553450.png)

Core loading component calling interface:

![image-20250411103915253](assets/image-20250411103915253.png)

### Component Analysis

**Core Loading Component**

The attacker designed multiple types of component loading methods in the core loading component, which can be classified based on the type value set by the attacker:

Type 1: Create thread to execute shellcode:

![image-20250411105420360](assets/image-20250411105420360.png)

Type 2: Reflective loading:

![image-20250411105731119](assets/image-20250411105731119.png)

Type 3: Injection execution:

![image-20250411110549996](assets/image-20250411110549996.png)

Type 4: Using LoadLibraryW for loading:

![image-20250411110748871](assets/image-20250411110748871.png)

The first three types need to be decrypted using the same algorithm and key:

![image-20250411111006586](assets/image-20250411111006586.png)

**Functional Components**

A total of 4 components were captured, divided into two types, as shown in the table below:

![image-20250411143258480](assets/image-20250411143258480.png)

### **Attribution**

**Algorithm Association**

In previously exposed attack incidents, DarkHotel has repeatedly used XOR algorithms for encryption and decryption. For example, the algorithm used in the organization's attack activity using the initplugins architecture in 2023:

![image-20250411142210095](assets/image-20250411142210095.png)

**Architecture Association**

The component loading framework used by DarkHotel in this incident and the initplugins framework captured in 2023 both decrypt and load by reading built-in file lists:

![image-20250411143814970](assets/image-20250411143814970.png)

**Component Association**

The components loaded in this captured attack activity are identical to those used in the 2023 initplugins attack activity.

![image-20250411152908026](assets/image-20250411152908026.png)

Based on the above associations, we are highly confident that the samples captured this time are the work of the DarkHotel organization. Compared to previous attack activities of this organization, this time DarkHotel has separated the remote control component (meterpreter) from functional components (keyboard logging, screen capture, and USB theft) for separate loading and operation. This may be to prevent the failure of the entire attack chain due to a single component being discovered, showing that the organization has been consistently working to improve the robustness of their weapons and increase the chances of successful attacks.

## 1.4 IOC

e14459863ac38df87e59e0a93a0fa438

d61308ddf2624e726fe83cd487dd6fe3

c92afb4d4fc5effd49cbc048d4355d1c

7cff54d8227393c352ee0859bc5cf01e