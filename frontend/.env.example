# Environment Configuration Example
# Copy this file to .env.local for development or .env.production for production

# =============================================================================
# ENVIRONMENT SETTINGS
# =============================================================================

# Environment identifier (development, staging, production)
NEXT_PUBLIC_ENV=development

# =============================================================================
# AWS COGNITO AUTHENTICATION
# =============================================================================

# AWS Region where your Cognito User Pool is located
NEXT_PUBLIC_AWS_REGION=us-east-1

# Cognito User Pool ID (found in AWS Cognito Console)
# Format: us-east-1_xxxxxxxxx
NEXT_PUBLIC_COGNITO_USER_POOL_ID=us-east-1_xxxxxxxxx

# Cognito App Client ID (found in AWS Cognito Console > App clients)
# Format: xxxxxxxxxxxxxxxxxxxxxxxxxx
NEXT_PUBLIC_COGNITO_CLIENT_ID=xxxxxxxxxxxxxxxxxxxxxxxxxx

# Cognito Domain for Hosted UI (without https://)
# Format: your-domain.auth.region.amazoncognito.com
# Or custom domain: auth.yourdomain.com
NEXT_PUBLIC_COGNITO_DOMAIN=your-domain.auth.us-east-1.amazoncognito.com

# =============================================================================
# REDIRECT URLS
# =============================================================================

# Comma-separated list of allowed redirect URLs after sign in
# Include all environments (local, staging, production)
NEXT_PUBLIC_REDIRECT_SIGN_IN=http://localhost:9001/auth/callback,https://staging.yourdomain.com/auth/callback,https://yourdomain.com/auth/callback

# Comma-separated list of allowed redirect URLs after sign out
# Include all environments (local, staging, production)
NEXT_PUBLIC_REDIRECT_SIGN_OUT=http://localhost:9001,https://staging.yourdomain.com,https://yourdomain.com

# =============================================================================
# API CONFIGURATION
# =============================================================================

# Backend API endpoint for subscription management and billing
# Development: http://localhost:3001 or your local API
# Production: https://api.yourdomain.com
NEXT_PUBLIC_API_ENDPOINT=https://api.yourdomain.com

# =============================================================================
# STRIPE PAYMENT PROCESSING
# =============================================================================

# Stripe Publishable Key (starts with pk_test_ for test mode, pk_live_ for live mode)
# Get this from Stripe Dashboard > Developers > API keys
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# Stripe Price IDs for Professional Plan
# Create these in Stripe Dashboard > Products > Add product > Add pricing
NEXT_PUBLIC_STRIPE_PROFESSIONAL_MONTHLY_PRICE_ID=price_xxxxxxxxxxxxxxxxxxxxx
NEXT_PUBLIC_STRIPE_PROFESSIONAL_ANNUAL_PRICE_ID=price_xxxxxxxxxxxxxxxxxxxxx

# Stripe Price IDs for Enterprise Plan (optional, can be handled via contact sales)
NEXT_PUBLIC_STRIPE_ENTERPRISE_MONTHLY_PRICE_ID=price_xxxxxxxxxxxxxxxxxxxxx
NEXT_PUBLIC_STRIPE_ENTERPRISE_ANNUAL_PRICE_ID=price_xxxxxxxxxxxxxxxxxxxxx

# =============================================================================
# AWS S3 STORAGE (OPTIONAL)
# =============================================================================

# S3 bucket name for file storage (if needed)
NEXT_PUBLIC_S3_BUCKET=your-bucket-name

# S3 region (should match your main AWS region)
NEXT_PUBLIC_S3_REGION=us-east-1

# =============================================================================
# ANALYTICS & MONITORING (OPTIONAL)
# =============================================================================

# Google Analytics Measurement ID (if using GA4)
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX

# Sentry DSN for error tracking (if using Sentry)
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/xxxxxxx

# =============================================================================
# FEATURE FLAGS (OPTIONAL)
# =============================================================================

# Enable/disable specific features
NEXT_PUBLIC_ENABLE_CHAT_WIDGET=true
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_ERROR_TRACKING=true

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Development server settings
DEV_SERVER_HOST=0.0.0.0
DEV_SERVER_PORT=9001

# Enable development features
NEXT_PUBLIC_DEBUG_MODE=false

# =============================================================================
# BACKEND ENVIRONMENT VARIABLES (for Lambda functions)
# These are not used by the frontend but documented for completeness
# =============================================================================

# Stripe Secret Key (server-side only, never expose to frontend)
# STRIPE_SECRET_KEY=sk_test_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# Stripe Webhook Secret (for verifying webhook signatures)
# STRIPE_WEBHOOK_SECRET=whsec_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# DynamoDB Table Names
# DYNAMODB_USERS_TABLE=gcandle-users-prod
# DYNAMODB_SUBSCRIPTIONS_TABLE=gcandle-subscriptions-prod
# DYNAMODB_USAGE_TABLE=gcandle-usage-prod

# SES Configuration for email notifications
# SES_FROM_EMAIL=<EMAIL>
# SES_REGION=us-east-1

# JWT Secret for API authentication
# JWT_SECRET=your-super-secret-jwt-key-here

# =============================================================================
# SETUP INSTRUCTIONS
# =============================================================================

# 1. Copy this file to .env.local for development
# 2. Replace all placeholder values (xxxxxxxxx) with actual values
# 3. Set up AWS Cognito User Pool and get the required IDs
# 4. Create Stripe account and get API keys
# 5. Configure redirect URLs in both Cognito and Stripe
# 6. Test authentication and payment flows
# 7. For production, use .env.production with production values

# =============================================================================
# SECURITY NOTES
# =============================================================================

# - Never commit .env.local or .env.production to version control
# - Use different Stripe keys for development (test) and production (live)
# - Regularly rotate API keys and secrets
# - Use AWS IAM roles with minimal required permissions
# - Enable MFA on all AWS and Stripe accounts
# - Monitor usage and set up billing alerts

# =============================================================================
# TROUBLESHOOTING
# =============================================================================

# Common issues:
# 1. CORS errors: Check API Gateway CORS settings
# 2. Auth failures: Verify Cognito redirect URLs match exactly
# 3. Payment issues: Ensure Stripe webhook endpoints are configured
# 4. Build failures: Check all required environment variables are set

# For support, check:
# - AWS Cognito documentation: https://docs.aws.amazon.com/cognito/
# - Stripe documentation: https://stripe.com/docs
# - Next.js documentation: https://nextjs.org/docs
