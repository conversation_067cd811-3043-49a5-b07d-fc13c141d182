{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "ignore": ["dist", ".next", "node_modules", "build", "src/generated/**"], "include": ["src/**/*.ts", "src/**/*.tsx"]}, "formatter": {"enabled": true, "indentStyle": "space"}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "correctness": {"noUnusedVariables": "off"}, "nursery": {"noImgElement": "off"}, "a11y": {"noAutofocus": "off", "noDistractingElements": "off", "noHeaderScope": "off", "noInteractiveElementToNoninteractiveRole": "off", "noLabelWithoutControl": "off", "noNoninteractiveElementToInteractiveRole": "off", "noNoninteractiveTabindex": "off", "noPositiveTabindex": "off", "noRedundantAlt": "off", "noRedundantRoles": "off", "noSvgWithoutTitle": "off", "useAltText": "off", "useKeyWithClickEvents": "off", "useKeyWithMouseEvents": "off", "useButtonType": "off"}}}, "javascript": {"formatter": {"quoteStyle": "double"}}}