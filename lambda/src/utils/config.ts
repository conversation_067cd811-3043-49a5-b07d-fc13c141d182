/**
 * Configuration utilities for Lambda functions
 */

export interface Config {
  stripe: {
    secretKey: string;
    webhookSecret: string;
    priceIds: {
      professional: {
        monthly: string;
        annual: string;
      };
      enterprise: {
        monthly: string;
        annual: string;
      };
    };
  };
  aws: {
    region: string;
    dynamodb: {
      usersTable: string;
      subscriptionsTable: string;
      usageTable: string;
      invoicesTable: string;
    };
    ses: {
      fromEmail: string;
      region: string;
    };
    cognito: {
      userPoolId: string;
      region: string;
    };
  };
  jwt: {
    secret: string;
  };
  app: {
    frontendUrl: string;
    apiUrl: string;
  };
}

/**
 * Get configuration from environment variables
 */
export function getConfig(): Config {
  const requiredEnvVars = [
    'STRIPE_SECRET_KEY',
    'STRIPE_WEBHOOK_SECRET',
    'DYNAMODB_USERS_TABLE',
    'DYNAMODB_SUBSCRIPTIONS_TABLE',
    'DYNAMODB_USAGE_TABLE',
    'DYNAMODB_INVOICES_TABLE',
    'SES_FROM_EMAIL',
    'COGNITO_USER_POOL_ID',
    'JWT_SECRET',
    'FRONTEND_URL',
    'API_URL'
  ];

  // Check for required environment variables
  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      throw new Error(`Missing required environment variable: ${envVar}`);
    }
  }

  return {
    stripe: {
      secretKey: process.env.STRIPE_SECRET_KEY!,
      webhookSecret: process.env.STRIPE_WEBHOOK_SECRET!,
      priceIds: {
        professional: {
          monthly: process.env.STRIPE_PROFESSIONAL_MONTHLY_PRICE_ID || '',
          annual: process.env.STRIPE_PROFESSIONAL_ANNUAL_PRICE_ID || '',
        },
        enterprise: {
          monthly: process.env.STRIPE_ENTERPRISE_MONTHLY_PRICE_ID || '',
          annual: process.env.STRIPE_ENTERPRISE_ANNUAL_PRICE_ID || '',
        },
      },
    },
    aws: {
      region: process.env.AWS_REGION || 'us-east-1',
      dynamodb: {
        usersTable: process.env.DYNAMODB_USERS_TABLE!,
        subscriptionsTable: process.env.DYNAMODB_SUBSCRIPTIONS_TABLE!,
        usageTable: process.env.DYNAMODB_USAGE_TABLE!,
        invoicesTable: process.env.DYNAMODB_INVOICES_TABLE!,
      },
      ses: {
        fromEmail: process.env.SES_FROM_EMAIL!,
        region: process.env.SES_REGION || process.env.AWS_REGION || 'us-east-1',
      },
      cognito: {
        userPoolId: process.env.COGNITO_USER_POOL_ID!,
        region: process.env.AWS_REGION || 'us-east-1',
      },
    },
    jwt: {
      secret: process.env.JWT_SECRET!,
    },
    app: {
      frontendUrl: process.env.FRONTEND_URL!,
      apiUrl: process.env.API_URL!,
    },
  };
}

/**
 * Validate configuration
 */
export function validateConfig(config: Config): void {
  // Validate Stripe price IDs
  if (!config.stripe.priceIds.professional.monthly || !config.stripe.priceIds.professional.annual) {
    console.warn('Professional plan price IDs not configured');
  }

  // Validate URLs
  try {
    new URL(config.app.frontendUrl);
    new URL(config.app.apiUrl);
  } catch (error) {
    throw new Error('Invalid URL configuration');
  }

  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(config.aws.ses.fromEmail)) {
    throw new Error('Invalid SES from email format');
  }
}

// Export singleton config instance
let configInstance: Config | null = null;

export function getConfigInstance(): Config {
  if (!configInstance) {
    configInstance = getConfig();
    validateConfig(configInstance);
  }
  return configInstance;
}
