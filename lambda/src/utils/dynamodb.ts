/**
 * DynamoDB utilities for Lambda functions
 */

import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { 
  DynamoDBDocumentClient, 
  GetCommand, 
  PutCommand, 
  UpdateCommand, 
  DeleteCommand, 
  QueryCommand,
  ScanCommand 
} from '@aws-sdk/lib-dynamodb';
import { getConfigInstance } from './config';
import { User, Subscription, Usage, Invoice } from '../types';

// Initialize DynamoDB client
const config = getConfigInstance();
const client = new DynamoDBClient({ region: config.aws.region });
const docClient = DynamoDBDocumentClient.from(client);

/**
 * User operations
 */
export class UserRepository {
  private tableName = config.aws.dynamodb.usersTable;

  async getUser(userId: string): Promise<User | null> {
    try {
      const result = await docClient.send(new GetCommand({
        TableName: this.tableName,
        Key: { userId },
      }));
      
      return result.Item as User || null;
    } catch (error) {
      console.error('Error getting user:', error);
      throw new Error('Failed to get user');
    }
  }

  async createUser(user: Omit<User, 'createdAt' | 'updatedAt'>): Promise<User> {
    const now = new Date().toISOString();
    const newUser: User = {
      ...user,
      createdAt: now,
      updatedAt: now,
    };

    try {
      await docClient.send(new PutCommand({
        TableName: this.tableName,
        Item: newUser,
        ConditionExpression: 'attribute_not_exists(userId)',
      }));
      
      return newUser;
    } catch (error) {
      if ((error as any).name === 'ConditionalCheckFailedException') {
        throw new Error('User already exists');
      }
      console.error('Error creating user:', error);
      throw new Error('Failed to create user');
    }
  }

  async updateUser(userId: string, updates: Partial<User>): Promise<User> {
    const now = new Date().toISOString();
    
    // Build update expression
    const updateExpressions: string[] = [];
    const expressionAttributeNames: Record<string, string> = {};
    const expressionAttributeValues: Record<string, any> = {};
    
    Object.entries(updates).forEach(([key, value]) => {
      if (key !== 'userId' && key !== 'createdAt') {
        updateExpressions.push(`#${key} = :${key}`);
        expressionAttributeNames[`#${key}`] = key;
        expressionAttributeValues[`:${key}`] = value;
      }
    });
    
    updateExpressions.push('#updatedAt = :updatedAt');
    expressionAttributeNames['#updatedAt'] = 'updatedAt';
    expressionAttributeValues[':updatedAt'] = now;

    try {
      const result = await docClient.send(new UpdateCommand({
        TableName: this.tableName,
        Key: { userId },
        UpdateExpression: `SET ${updateExpressions.join(', ')}`,
        ExpressionAttributeNames: expressionAttributeNames,
        ExpressionAttributeValues: expressionAttributeValues,
        ReturnValues: 'ALL_NEW',
      }));
      
      return result.Attributes as User;
    } catch (error) {
      console.error('Error updating user:', error);
      throw new Error('Failed to update user');
    }
  }

  async getUserByEmail(email: string): Promise<User | null> {
    try {
      const result = await docClient.send(new ScanCommand({
        TableName: this.tableName,
        FilterExpression: 'email = :email',
        ExpressionAttributeValues: {
          ':email': email,
        },
      }));
      
      return result.Items?.[0] as User || null;
    } catch (error) {
      console.error('Error getting user by email:', error);
      throw new Error('Failed to get user by email');
    }
  }

  async getUserByStripeCustomerId(stripeCustomerId: string): Promise<User | null> {
    try {
      const result = await docClient.send(new ScanCommand({
        TableName: this.tableName,
        FilterExpression: 'stripeCustomerId = :stripeCustomerId',
        ExpressionAttributeValues: {
          ':stripeCustomerId': stripeCustomerId,
        },
      }));
      
      return result.Items?.[0] as User || null;
    } catch (error) {
      console.error('Error getting user by Stripe customer ID:', error);
      throw new Error('Failed to get user by Stripe customer ID');
    }
  }
}

/**
 * Subscription operations
 */
export class SubscriptionRepository {
  private tableName = config.aws.dynamodb.subscriptionsTable;

  async getSubscription(userId: string, subscriptionId: string): Promise<Subscription | null> {
    try {
      const result = await docClient.send(new GetCommand({
        TableName: this.tableName,
        Key: { userId, subscriptionId },
      }));
      
      return result.Item as Subscription || null;
    } catch (error) {
      console.error('Error getting subscription:', error);
      throw new Error('Failed to get subscription');
    }
  }

  async getUserSubscriptions(userId: string): Promise<Subscription[]> {
    try {
      const result = await docClient.send(new QueryCommand({
        TableName: this.tableName,
        KeyConditionExpression: 'userId = :userId',
        ExpressionAttributeValues: {
          ':userId': userId,
        },
      }));
      
      return result.Items as Subscription[] || [];
    } catch (error) {
      console.error('Error getting user subscriptions:', error);
      throw new Error('Failed to get user subscriptions');
    }
  }

  async getActiveSubscription(userId: string): Promise<Subscription | null> {
    const subscriptions = await this.getUserSubscriptions(userId);
    return subscriptions.find(sub => sub.status === 'active' || sub.status === 'trialing') || null;
  }

  async createSubscription(subscription: Omit<Subscription, 'createdAt' | 'updatedAt'>): Promise<Subscription> {
    const now = new Date().toISOString();
    const newSubscription: Subscription = {
      ...subscription,
      createdAt: now,
      updatedAt: now,
    };

    try {
      await docClient.send(new PutCommand({
        TableName: this.tableName,
        Item: newSubscription,
      }));
      
      return newSubscription;
    } catch (error) {
      console.error('Error creating subscription:', error);
      throw new Error('Failed to create subscription');
    }
  }

  async updateSubscription(
    userId: string, 
    subscriptionId: string, 
    updates: Partial<Subscription>
  ): Promise<Subscription> {
    const now = new Date().toISOString();
    
    // Build update expression
    const updateExpressions: string[] = [];
    const expressionAttributeNames: Record<string, string> = {};
    const expressionAttributeValues: Record<string, any> = {};
    
    Object.entries(updates).forEach(([key, value]) => {
      if (key !== 'userId' && key !== 'subscriptionId' && key !== 'createdAt') {
        updateExpressions.push(`#${key} = :${key}`);
        expressionAttributeNames[`#${key}`] = key;
        expressionAttributeValues[`:${key}`] = value;
      }
    });
    
    updateExpressions.push('#updatedAt = :updatedAt');
    expressionAttributeNames['#updatedAt'] = 'updatedAt';
    expressionAttributeValues[':updatedAt'] = now;

    try {
      const result = await docClient.send(new UpdateCommand({
        TableName: this.tableName,
        Key: { userId, subscriptionId },
        UpdateExpression: `SET ${updateExpressions.join(', ')}`,
        ExpressionAttributeNames: expressionAttributeNames,
        ExpressionAttributeValues: expressionAttributeValues,
        ReturnValues: 'ALL_NEW',
      }));
      
      return result.Attributes as Subscription;
    } catch (error) {
      console.error('Error updating subscription:', error);
      throw new Error('Failed to update subscription');
    }
  }

  async getSubscriptionByStripeId(stripeSubscriptionId: string): Promise<Subscription | null> {
    try {
      const result = await docClient.send(new ScanCommand({
        TableName: this.tableName,
        FilterExpression: 'stripeSubscriptionId = :stripeSubscriptionId',
        ExpressionAttributeValues: {
          ':stripeSubscriptionId': stripeSubscriptionId,
        },
      }));
      
      return result.Items?.[0] as Subscription || null;
    } catch (error) {
      console.error('Error getting subscription by Stripe ID:', error);
      throw new Error('Failed to get subscription by Stripe ID');
    }
  }
}

/**
 * Usage operations
 */
export class UsageRepository {
  private tableName = config.aws.dynamodb.usageTable;

  async getUsage(userId: string, date: string): Promise<Usage | null> {
    try {
      const result = await docClient.send(new GetCommand({
        TableName: this.tableName,
        Key: { userId, date },
      }));
      
      return result.Item as Usage || null;
    } catch (error) {
      console.error('Error getting usage:', error);
      throw new Error('Failed to get usage');
    }
  }

  async updateUsage(userId: string, date: string, updates: Partial<Usage>): Promise<Usage> {
    const now = new Date().toISOString();
    
    try {
      const result = await docClient.send(new UpdateCommand({
        TableName: this.tableName,
        Key: { userId, date },
        UpdateExpression: 'SET searches = if_not_exists(searches, :zero) + :searchIncrement, apiCalls = if_not_exists(apiCalls, :zero) + :apiIncrement, lastUpdated = :lastUpdated',
        ExpressionAttributeValues: {
          ':zero': 0,
          ':searchIncrement': updates.searches || 0,
          ':apiIncrement': updates.apiCalls || 0,
          ':lastUpdated': now,
        },
        ReturnValues: 'ALL_NEW',
      }));
      
      return result.Attributes as Usage;
    } catch (error) {
      console.error('Error updating usage:', error);
      throw new Error('Failed to update usage');
    }
  }
}

// Export repository instances
export const userRepository = new UserRepository();
export const subscriptionRepository = new SubscriptionRepository();
export const usageRepository = new UsageRepository();
