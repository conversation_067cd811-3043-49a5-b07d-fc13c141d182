/**
 * Stripe utilities for Lambda functions
 */

import Stripe from 'stripe';
import { getConfigInstance } from './config';

// Initialize Stripe client
const config = getConfigInstance();
const stripe = new Stripe(config.stripe.secretKey, {
  apiVersion: '2023-10-16',
});

/**
 * Create a Stripe customer
 */
export async function createStripeCustomer(
  email: string,
  name?: string,
  metadata?: Record<string, string>
): Promise<Stripe.Customer> {
  try {
    const customer = await stripe.customers.create({
      email,
      name,
      metadata: {
        source: 'gcandle-saas',
        ...metadata,
      },
    });
    
    return customer;
  } catch (error) {
    console.error('Error creating Stripe customer:', error);
    throw new Error('Failed to create Stripe customer');
  }
}

/**
 * Create a checkout session for subscription
 */
export async function createCheckoutSession(
  priceId: string,
  customerId?: string,
  successUrl?: string,
  cancelUrl?: string,
  metadata?: Record<string, string>
): Promise<Stripe.Checkout.Session> {
  try {
    const sessionParams: Stripe.Checkout.SessionCreateParams = {
      mode: 'subscription',
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      success_url: successUrl || `${config.app.frontendUrl}/profile?success=true&session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: cancelUrl || `${config.app.frontendUrl}/plan?canceled=true`,
      metadata: {
        source: 'gcandle-saas',
        ...metadata,
      },
      subscription_data: {
        metadata: {
          source: 'gcandle-saas',
          ...metadata,
        },
      },
    };

    // If customer ID is provided, use it; otherwise let Stripe create a new customer
    if (customerId) {
      sessionParams.customer = customerId;
    } else {
      sessionParams.customer_creation = 'always';
    }

    const session = await stripe.checkout.sessions.create(sessionParams);
    
    return session;
  } catch (error) {
    console.error('Error creating checkout session:', error);
    throw new Error('Failed to create checkout session');
  }
}

/**
 * Create a customer portal session
 */
export async function createPortalSession(
  customerId: string,
  returnUrl?: string
): Promise<Stripe.BillingPortal.Session> {
  try {
    const session = await stripe.billingPortal.sessions.create({
      customer: customerId,
      return_url: returnUrl || `${config.app.frontendUrl}/profile`,
    });
    
    return session;
  } catch (error) {
    console.error('Error creating portal session:', error);
    throw new Error('Failed to create portal session');
  }
}

/**
 * Get subscription by ID
 */
export async function getStripeSubscription(subscriptionId: string): Promise<Stripe.Subscription> {
  try {
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);
    return subscription;
  } catch (error) {
    console.error('Error getting Stripe subscription:', error);
    throw new Error('Failed to get Stripe subscription');
  }
}

/**
 * Cancel subscription
 */
export async function cancelStripeSubscription(
  subscriptionId: string,
  cancelAtPeriodEnd: boolean = true
): Promise<Stripe.Subscription> {
  try {
    const subscription = await stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: cancelAtPeriodEnd,
    });
    
    return subscription;
  } catch (error) {
    console.error('Error canceling Stripe subscription:', error);
    throw new Error('Failed to cancel Stripe subscription');
  }
}

/**
 * Get customer by ID
 */
export async function getStripeCustomer(customerId: string): Promise<Stripe.Customer> {
  try {
    const customer = await stripe.customers.retrieve(customerId);
    
    if (customer.deleted) {
      throw new Error('Customer has been deleted');
    }
    
    return customer as Stripe.Customer;
  } catch (error) {
    console.error('Error getting Stripe customer:', error);
    throw new Error('Failed to get Stripe customer');
  }
}

/**
 * Get customer subscriptions
 */
export async function getCustomerSubscriptions(customerId: string): Promise<Stripe.Subscription[]> {
  try {
    const subscriptions = await stripe.subscriptions.list({
      customer: customerId,
      status: 'all',
    });
    
    return subscriptions.data;
  } catch (error) {
    console.error('Error getting customer subscriptions:', error);
    throw new Error('Failed to get customer subscriptions');
  }
}

/**
 * Verify webhook signature
 */
export function verifyWebhookSignature(
  payload: string | Buffer,
  signature: string
): Stripe.Event {
  try {
    const event = stripe.webhooks.constructEvent(
      payload,
      signature,
      config.stripe.webhookSecret
    );
    
    return event;
  } catch (error) {
    console.error('Error verifying webhook signature:', error);
    throw new Error('Invalid webhook signature');
  }
}

/**
 * Get price information
 */
export async function getStripePrice(priceId: string): Promise<Stripe.Price> {
  try {
    const price = await stripe.prices.retrieve(priceId);
    return price;
  } catch (error) {
    console.error('Error getting Stripe price:', error);
    throw new Error('Failed to get Stripe price');
  }
}

/**
 * Get product information
 */
export async function getStripeProduct(productId: string): Promise<Stripe.Product> {
  try {
    const product = await stripe.products.retrieve(productId);
    return product;
  } catch (error) {
    console.error('Error getting Stripe product:', error);
    throw new Error('Failed to get Stripe product');
  }
}

/**
 * Map Stripe subscription to our subscription format
 */
export function mapStripeSubscription(
  stripeSubscription: Stripe.Subscription,
  userId: string
): {
  subscriptionId: string;
  userId: string;
  stripeSubscriptionId: string;
  stripeCustomerId: string;
  status: string;
  planId: string;
  planName: string;
  priceId: string;
  currentPeriodStart: number;
  currentPeriodEnd: number;
  cancelAtPeriodEnd: boolean;
  trialEnd?: number;
} {
  const priceId = stripeSubscription.items.data[0]?.price.id || '';
  
  // Map price ID to plan ID
  let planId = 'unknown';
  let planName = 'Unknown Plan';
  
  if (priceId === config.stripe.priceIds.professional.monthly || 
      priceId === config.stripe.priceIds.professional.annual) {
    planId = 'professional';
    planName = 'Professional';
  } else if (priceId === config.stripe.priceIds.enterprise.monthly || 
             priceId === config.stripe.priceIds.enterprise.annual) {
    planId = 'enterprise';
    planName = 'Enterprise';
  }

  return {
    subscriptionId: `sub_${userId}_${Date.now()}`,
    userId,
    stripeSubscriptionId: stripeSubscription.id,
    stripeCustomerId: stripeSubscription.customer as string,
    status: stripeSubscription.status,
    planId,
    planName,
    priceId,
    currentPeriodStart: stripeSubscription.current_period_start,
    currentPeriodEnd: stripeSubscription.current_period_end,
    cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end || false,
    trialEnd: stripeSubscription.trial_end || undefined,
  };
}

export { stripe };
