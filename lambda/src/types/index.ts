/**
 * Type definitions for Gcandle SaaS Lambda functions
 */

export interface User {
  userId: string;
  email: string;
  name?: string;
  stripeCustomerId?: string;
  plan: 'free' | 'professional' | 'enterprise';
  createdAt: string;
  updatedAt: string;
  emailVerified: boolean;
}

export interface Subscription {
  subscriptionId: string;
  userId: string;
  stripeSubscriptionId: string;
  stripeCustomerId: string;
  status: SubscriptionStatus;
  planId: string;
  planName: string;
  priceId: string;
  currentPeriodStart: number;
  currentPeriodEnd: number;
  cancelAtPeriodEnd: boolean;
  trialEnd?: number;
  createdAt: string;
  updatedAt: string;
}

export type SubscriptionStatus = 
  | 'active'
  | 'canceled'
  | 'incomplete'
  | 'incomplete_expired'
  | 'past_due'
  | 'trialing'
  | 'unpaid';

export interface Usage {
  userId: string;
  date: string; // YYYY-MM-DD format
  searches: number;
  apiCalls: number;
  lastUpdated: string;
}

export interface Invoice {
  invoiceId: string;
  userId: string;
  stripeInvoiceId: string;
  amount: number;
  currency: string;
  status: 'draft' | 'open' | 'paid' | 'uncollectible' | 'void';
  createdAt: string;
  paidAt?: string;
}

export interface PlanLimits {
  searchesPerDay: number;
  apiCallsPerMonth: number;
  features: string[];
}

export const PLAN_LIMITS: Record<string, PlanLimits> = {
  free: {
    searchesPerDay: 10,
    apiCallsPerMonth: 1000,
    features: ['basic_search', 'community_api', 'email_support', 'basic_reporting']
  },
  professional: {
    searchesPerDay: 1000,
    apiCallsPerMonth: 50000,
    features: [
      'basic_search', 'community_api', 'email_support', 'basic_reporting',
      'advanced_search', 'full_api', 'priority_support', 'advanced_analytics',
      'custom_dashboards', 'threat_hunting'
    ]
  },
  enterprise: {
    searchesPerDay: -1, // unlimited
    apiCallsPerMonth: -1, // unlimited
    features: [
      'basic_search', 'community_api', 'email_support', 'basic_reporting',
      'advanced_search', 'full_api', 'priority_support', 'advanced_analytics',
      'custom_dashboards', 'threat_hunting', 'unlimited_search', 'premium_api',
      'phone_support', 'custom_integrations', 'dedicated_manager', 'sla_guarantees'
    ]
  }
};

export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface CreateCheckoutSessionRequest {
  priceId: string;
  customerId?: string;
  successUrl?: string;
  cancelUrl?: string;
}

export interface CreatePortalSessionRequest {
  customerId: string;
  returnUrl?: string;
}

export interface CognitoUser {
  sub: string;
  email: string;
  name?: string;
  email_verified: boolean;
}

export interface JWTPayload {
  sub: string;
  email: string;
  name?: string;
  email_verified: boolean;
  iat: number;
  exp: number;
  aud: string;
  iss: string;
}
