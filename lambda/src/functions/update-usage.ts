/**
 * Lambda function to update user usage statistics
 * Endpoint: POST /user/usage
 */

import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { 
  getAuthenticatedUser, 
  validateRequestBody, 
  successResponse, 
  errorResponse, 
  handleCORS 
} from '../utils/auth';
import { userRepository, usageRepository } from '../utils/dynamodb';
import { PLAN_LIMITS } from '../types';

interface UpdateUsageRequest {
  searches?: number;
  apiCalls?: number;
  date?: string; // Optional, defaults to today
}

export const handler = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  console.log('Update usage request:', JSON.stringify(event, null, 2));

  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return handleCORS();
  }

  try {
    // Authenticate user
    const cognitoUser = getAuthenticatedUser(event);
    
    // Validate request body
    const { searches = 0, apiCalls = 0, date } = validateRequestBody<UpdateUsageRequest>(
      event,
      [] // No required fields, but at least one of searches or apiCalls should be > 0
    );

    if (searches === 0 && apiCalls === 0) {
      return errorResponse('At least one of searches or apiCalls must be greater than 0', 400);
    }

    // Get user from database
    const user = await userRepository.getUser(cognitoUser.sub);
    
    if (!user) {
      return errorResponse('User not found', 404);
    }

    // Use provided date or default to today
    const usageDate = date || new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    
    // Get plan limits
    const planLimits = PLAN_LIMITS[user.plan] || PLAN_LIMITS.free;
    
    // Get current usage for the date
    const currentUsage = await usageRepository.getUsage(user.userId, usageDate);
    
    // Check limits before updating
    const newSearches = (currentUsage?.searches || 0) + searches;
    const newApiCalls = (currentUsage?.apiCalls || 0) + apiCalls;
    
    // Check search limits
    if (planLimits.searchesPerDay !== -1 && newSearches > planLimits.searchesPerDay) {
      return errorResponse(
        `Search limit exceeded. Daily limit: ${planLimits.searchesPerDay}, current usage: ${currentUsage?.searches || 0}`,
        429
      );
    }
    
    // Check API call limits (simplified daily check - you might want monthly aggregation)
    if (planLimits.apiCallsPerMonth !== -1 && newApiCalls > (planLimits.apiCallsPerMonth / 30)) {
      return errorResponse(
        `API call limit exceeded. Daily limit: ${Math.floor(planLimits.apiCallsPerMonth / 30)}, current usage: ${currentUsage?.apiCalls || 0}`,
        429
      );
    }

    // Update usage
    const updatedUsage = await usageRepository.updateUsage(user.userId, usageDate, {
      searches,
      apiCalls,
    });

    // Calculate usage percentages
    const searchUsagePercent = planLimits.searchesPerDay === -1 
      ? 0 
      : Math.min(100, (updatedUsage.searches / planLimits.searchesPerDay) * 100);
    
    const apiUsagePercent = planLimits.apiCallsPerMonth === -1 
      ? 0 
      : Math.min(100, (updatedUsage.apiCalls / (planLimits.apiCallsPerMonth / 30)) * 100);

    return successResponse({
      usage: updatedUsage,
      limits: {
        searchesPerDay: planLimits.searchesPerDay,
        apiCallsPerMonth: planLimits.apiCallsPerMonth,
      },
      percentages: {
        searches: searchUsagePercent,
        apiCalls: apiUsagePercent,
      },
      remaining: {
        searches: planLimits.searchesPerDay === -1 
          ? -1 
          : Math.max(0, planLimits.searchesPerDay - updatedUsage.searches),
        apiCalls: planLimits.apiCallsPerMonth === -1 
          ? -1 
          : Math.max(0, Math.floor(planLimits.apiCallsPerMonth / 30) - updatedUsage.apiCalls),
      },
    });

  } catch (error) {
    console.error('Error updating usage:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('No authorization token')) {
        return errorResponse('Authentication required', 401);
      }
      if (error.message.includes('Invalid token') || error.message.includes('Token expired')) {
        return errorResponse('Invalid or expired token', 401);
      }
      if (error.message.includes('Missing required field')) {
        return errorResponse(error.message, 400);
      }
      
      return errorResponse(error.message, 500);
    }
    
    return errorResponse('Internal server error', 500);
  }
};
