/**
 * Lambda function to create Stripe customer portal session
 * Endpoint: POST /stripe/create-portal-session
 */

import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { 
  getAuthenticatedUser, 
  validateRequestBody, 
  successResponse, 
  errorResponse, 
  handleCORS 
} from '../utils/auth';
import { userRepository } from '../utils/dynamodb';
import { createPortalSession } from '../utils/stripe';
import { CreatePortalSessionRequest } from '../types';

export const handler = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  console.log('Create portal session request:', JSON.stringify(event, null, 2));

  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return handleCORS();
  }

  try {
    // Authenticate user
    const cognitoUser = getAuthenticatedUser(event);
    
    // Validate request body
    const { customerId, returnUrl } = validateRequestBody<CreatePortalSessionRequest>(
      event,
      ['customerId']
    );

    // Get user from database
    const user = await userRepository.getUser(cognitoUser.sub);
    
    if (!user) {
      return errorResponse('User not found', 404);
    }

    // Verify that the customer ID belongs to this user
    if (user.stripeCustomerId !== customerId) {
      return errorResponse('Invalid customer ID', 403);
    }

    // Create portal session
    const session = await createPortalSession(customerId, returnUrl);

    return successResponse({
      url: session.url,
    });

  } catch (error) {
    console.error('Error creating portal session:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('No authorization token')) {
        return errorResponse('Authentication required', 401);
      }
      if (error.message.includes('Invalid token') || error.message.includes('Token expired')) {
        return errorResponse('Invalid or expired token', 401);
      }
      if (error.message.includes('Missing required field')) {
        return errorResponse(error.message, 400);
      }
      
      return errorResponse(error.message, 500);
    }
    
    return errorResponse('Internal server error', 500);
  }
};

// Export for testing
export { CreatePortalSessionRequest };
