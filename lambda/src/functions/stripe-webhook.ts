/**
 * <PERSON><PERSON> function to handle Stripe webhooks
 * Endpoint: POST /stripe/webhook
 */

import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import Stripe from 'stripe';
import { successResponse, errorResponse } from '../utils/auth';
import { userRepository, subscriptionRepository } from '../utils/dynamodb';
import { verifyWebhookSignature, mapStripeSubscription } from '../utils/stripe';

export const handler = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  console.log('Stripe webhook received:', {
    headers: event.headers,
    bodyLength: event.body?.length || 0,
  });

  try {
    // Get Stripe signature from headers
    const signature = event.headers['stripe-signature'] || event.headers['Stripe-Signature'];
    
    if (!signature) {
      return errorResponse('Missing Stripe signature', 400);
    }

    if (!event.body) {
      return errorResponse('Missing request body', 400);
    }

    // Verify webhook signature
    const stripeEvent = verifyWebhookSignature(event.body, signature);
    
    console.log('Processing Stripe event:', {
      id: stripeEvent.id,
      type: stripeEvent.type,
    });

    // Handle different event types
    switch (stripeEvent.type) {
      case 'checkout.session.completed':
        await handleCheckoutSessionCompleted(stripeEvent.data.object as Stripe.Checkout.Session);
        break;
        
      case 'customer.subscription.created':
        await handleSubscriptionCreated(stripeEvent.data.object as Stripe.Subscription);
        break;
        
      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(stripeEvent.data.object as Stripe.Subscription);
        break;
        
      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(stripeEvent.data.object as Stripe.Subscription);
        break;
        
      case 'invoice.payment_succeeded':
        await handleInvoicePaymentSucceeded(stripeEvent.data.object as Stripe.Invoice);
        break;
        
      case 'invoice.payment_failed':
        await handleInvoicePaymentFailed(stripeEvent.data.object as Stripe.Invoice);
        break;
        
      default:
        console.log(`Unhandled event type: ${stripeEvent.type}`);
    }

    return successResponse({ received: true });

  } catch (error) {
    console.error('Error processing webhook:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('Invalid webhook signature')) {
        return errorResponse('Invalid webhook signature', 400);
      }
      
      return errorResponse(error.message, 500);
    }
    
    return errorResponse('Internal server error', 500);
  }
};

/**
 * Handle checkout session completed
 */
async function handleCheckoutSessionCompleted(session: Stripe.Checkout.Session) {
  console.log('Processing checkout session completed:', session.id);
  
  try {
    const customerId = session.customer as string;
    const subscriptionId = session.subscription as string;
    
    if (!customerId || !subscriptionId) {
      console.warn('Missing customer or subscription ID in checkout session');
      return;
    }

    // Find user by Stripe customer ID
    const user = await userRepository.getUserByStripeCustomerId(customerId);
    
    if (!user) {
      console.error('User not found for customer ID:', customerId);
      return;
    }

    console.log('Checkout completed for user:', user.userId);
  } catch (error) {
    console.error('Error handling checkout session completed:', error);
    throw error;
  }
}

/**
 * Handle subscription created
 */
async function handleSubscriptionCreated(subscription: Stripe.Subscription) {
  console.log('Processing subscription created:', subscription.id);
  
  try {
    const customerId = subscription.customer as string;
    
    // Find user by Stripe customer ID
    const user = await userRepository.getUserByStripeCustomerId(customerId);
    
    if (!user) {
      console.error('User not found for customer ID:', customerId);
      return;
    }

    // Map Stripe subscription to our format
    const mappedSubscription = mapStripeSubscription(subscription, user.userId);

    // Create subscription in database
    await subscriptionRepository.createSubscription({
      ...mappedSubscription,
      status: mappedSubscription.status as any
    });
    
    // Update user plan
    await userRepository.updateUser(user.userId, {
      plan: mappedSubscription.planId as any,
    });

    console.log('Subscription created for user:', user.userId);
  } catch (error) {
    console.error('Error handling subscription created:', error);
    throw error;
  }
}

/**
 * Handle subscription updated
 */
async function handleSubscriptionUpdated(subscription: Stripe.Subscription) {
  console.log('Processing subscription updated:', subscription.id);
  
  try {
    // Find existing subscription in database
    const existingSubscription = await subscriptionRepository.getSubscriptionByStripeId(subscription.id);
    
    if (!existingSubscription) {
      console.warn('Subscription not found in database:', subscription.id);
      return;
    }

    // Update subscription in database
    await subscriptionRepository.updateSubscription(
      existingSubscription.userId,
      existingSubscription.subscriptionId,
      {
        status: subscription.status as any,
        currentPeriodStart: subscription.current_period_start,
        currentPeriodEnd: subscription.current_period_end,
        cancelAtPeriodEnd: subscription.cancel_at_period_end || false,
        trialEnd: subscription.trial_end || undefined,
      }
    );

    // Update user plan if subscription is active
    if (subscription.status === 'active' || subscription.status === 'trialing') {
      const mappedSubscription = mapStripeSubscription(subscription, existingSubscription.userId);
      await userRepository.updateUser(existingSubscription.userId, {
        plan: mappedSubscription.planId as any,
      });
    } else if (subscription.status === 'canceled' || subscription.status === 'unpaid') {
      // Downgrade to free plan
      await userRepository.updateUser(existingSubscription.userId, {
        plan: 'free',
      });
    }

    console.log('Subscription updated for user:', existingSubscription.userId);
  } catch (error) {
    console.error('Error handling subscription updated:', error);
    throw error;
  }
}

/**
 * Handle subscription deleted
 */
async function handleSubscriptionDeleted(subscription: Stripe.Subscription) {
  console.log('Processing subscription deleted:', subscription.id);
  
  try {
    // Find existing subscription in database
    const existingSubscription = await subscriptionRepository.getSubscriptionByStripeId(subscription.id);
    
    if (!existingSubscription) {
      console.warn('Subscription not found in database:', subscription.id);
      return;
    }

    // Update subscription status to canceled
    await subscriptionRepository.updateSubscription(
      existingSubscription.userId,
      existingSubscription.subscriptionId,
      {
        status: 'canceled',
      }
    );

    // Downgrade user to free plan
    await userRepository.updateUser(existingSubscription.userId, {
      plan: 'free',
    });

    console.log('Subscription deleted for user:', existingSubscription.userId);
  } catch (error) {
    console.error('Error handling subscription deleted:', error);
    throw error;
  }
}

/**
 * Handle invoice payment succeeded
 */
async function handleInvoicePaymentSucceeded(invoice: Stripe.Invoice) {
  console.log('Processing invoice payment succeeded:', invoice.id);
  
  try {
    const customerId = invoice.customer as string;
    
    // Find user by Stripe customer ID
    const user = await userRepository.getUserByStripeCustomerId(customerId);
    
    if (!user) {
      console.error('User not found for customer ID:', customerId);
      return;
    }

    // TODO: Send payment confirmation email
    // TODO: Update invoice record in database
    
    console.log('Invoice payment succeeded for user:', user.userId);
  } catch (error) {
    console.error('Error handling invoice payment succeeded:', error);
    throw error;
  }
}

/**
 * Handle invoice payment failed
 */
async function handleInvoicePaymentFailed(invoice: Stripe.Invoice) {
  console.log('Processing invoice payment failed:', invoice.id);
  
  try {
    const customerId = invoice.customer as string;
    
    // Find user by Stripe customer ID
    const user = await userRepository.getUserByStripeCustomerId(customerId);
    
    if (!user) {
      console.error('User not found for customer ID:', customerId);
      return;
    }

    // TODO: Send payment failure notification email
    // TODO: Handle subscription status based on payment failure
    
    console.log('Invoice payment failed for user:', user.userId);
  } catch (error) {
    console.error('Error handling invoice payment failed:', error);
    throw error;
  }
}
