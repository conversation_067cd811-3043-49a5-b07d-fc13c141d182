/**
 * Lambda function to get user profile information
 * Endpoint: GET /user/profile
 */

import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { 
  getAuthenticatedUser, 
  successResponse, 
  errorResponse, 
  handleCORS 
} from '../utils/auth';
import { userRepository, subscriptionRepository, usageRepository } from '../utils/dynamodb';
import { PLAN_LIMITS } from '../types';

export const handler = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  console.log('Get user profile request:', JSON.stringify(event, null, 2));

  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return handleCORS();
  }

  try {
    // Authenticate user
    const cognitoUser = getAuthenticatedUser(event);
    
    // Get user from database
    let user = await userRepository.getUser(cognitoUser.sub);
    
    if (!user) {
      // Create new user if doesn't exist
      user = await userRepository.createUser({
        userId: cognitoUser.sub,
        email: cognitoUser.email,
        name: cognitoUser.name,
        plan: 'free',
        emailVerified: cognitoUser.email_verified,
      });
    }

    // Get active subscription
    const subscription = await subscriptionRepository.getActiveSubscription(user.userId);
    
    // Get current usage (today)
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    const usage = await usageRepository.getUsage(user.userId, today);
    
    // Get plan limits
    const planLimits = PLAN_LIMITS[user.plan] || PLAN_LIMITS.free;
    
    // Calculate usage percentages
    const searchUsagePercent = planLimits.searchesPerDay === -1 
      ? 0 
      : Math.min(100, ((usage?.searches || 0) / planLimits.searchesPerDay) * 100);
    
    // Get monthly API usage (simplified - you might want to aggregate over the month)
    const apiUsagePercent = planLimits.apiCallsPerMonth === -1 
      ? 0 
      : Math.min(100, ((usage?.apiCalls || 0) / planLimits.apiCallsPerMonth) * 100);

    const response = {
      user: {
        userId: user.userId,
        email: user.email,
        name: user.name,
        plan: user.plan,
        emailVerified: user.emailVerified,
        stripeCustomerId: user.stripeCustomerId,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      },
      subscription: subscription ? {
        subscriptionId: subscription.subscriptionId,
        status: subscription.status,
        planId: subscription.planId,
        planName: subscription.planName,
        currentPeriodStart: subscription.currentPeriodStart,
        currentPeriodEnd: subscription.currentPeriodEnd,
        cancelAtPeriodEnd: subscription.cancelAtPeriodEnd,
        trialEnd: subscription.trialEnd,
      } : null,
      usage: {
        today: {
          searches: usage?.searches || 0,
          apiCalls: usage?.apiCalls || 0,
          date: today,
        },
        limits: {
          searchesPerDay: planLimits.searchesPerDay,
          apiCallsPerMonth: planLimits.apiCallsPerMonth,
        },
        percentages: {
          searches: searchUsagePercent,
          apiCalls: apiUsagePercent,
        },
      },
      features: {
        available: planLimits.features,
        hasFeature: (feature: string) => planLimits.features.includes(feature),
      },
    };

    return successResponse(response);

  } catch (error) {
    console.error('Error getting user profile:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('No authorization token')) {
        return errorResponse('Authentication required', 401);
      }
      if (error.message.includes('Invalid token') || error.message.includes('Token expired')) {
        return errorResponse('Invalid or expired token', 401);
      }
      
      return errorResponse(error.message, 500);
    }
    
    return errorResponse('Internal server error', 500);
  }
};
