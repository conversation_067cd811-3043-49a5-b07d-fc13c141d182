# Gcandle SaaS Lambda Functions

这个目录包含了支持Gcandle SaaS订阅付费功能的所有Lambda函数。

## 🏗 架构概述

### API端点
- `POST /stripe/create-checkout-session` - 创建Stripe结账会话
- `POST /stripe/create-portal-session` - 创建Stripe客户门户会话
- `GET /stripe/subscription` - 获取用户订阅信息
- `POST /stripe/subscription/{id}/cancel` - 取消订阅
- `POST /stripe/webhook` - 处理Stripe webhook事件
- `GET /user/profile` - 获取用户资料和订阅状态
- `POST /user/usage` - 更新用户使用统计

### DynamoDB表结构
- **Users表**: 用户基本信息和Stripe客户ID
- **Subscriptions表**: 订阅详情和状态
- **Usage表**: 用户使用统计（搜索次数、API调用等）
- **Invoices表**: 发票记录

## 🚀 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，填入实际的配置值
```

### 3. 编译TypeScript
```bash
npm run build
```

### 4. 本地开发
```bash
# 安装serverless框架
npm install -g serverless

# 本地运行
serverless offline
```

### 5. 部署到AWS
```bash
# 部署到开发环境
serverless deploy --stage dev

# 部署到生产环境
serverless deploy --stage prod
```

## 📋 环境变量配置

### 必需的环境变量

#### Stripe配置
```bash
STRIPE_SECRET_KEY=sk_test_xxxxxxxxxxxxx  # Stripe密钥
STRIPE_WEBHOOK_SECRET=whsec_xxxxxxxxxxxxx  # Webhook签名密钥
STRIPE_PROFESSIONAL_MONTHLY_PRICE_ID=price_xxxxxxxxxxxxx  # 专业版月付价格ID
STRIPE_PROFESSIONAL_ANNUAL_PRICE_ID=price_xxxxxxxxxxxxx   # 专业版年付价格ID
```

#### AWS配置
```bash
COGNITO_USER_POOL_ID=us-east-1_xxxxxxxxx  # Cognito用户池ID
SES_FROM_EMAIL=<EMAIL>     # SES发件邮箱
JWT_SECRET=your-jwt-secret-key            # JWT密钥
```

#### 应用配置
```bash
FRONTEND_URL=https://yourdomain.com       # 前端URL
API_URL=https://api.yourdomain.com        # API网关URL
```

## 🔧 函数详解

### 1. create-checkout-session.ts
**功能**: 创建Stripe结账会话
- 验证用户身份
- 创建或获取Stripe客户
- 生成结账会话URL

**请求示例**:
```json
{
  "priceId": "price_xxxxxxxxxxxxx",
  "successUrl": "https://yourdomain.com/profile?success=true",
  "cancelUrl": "https://yourdomain.com/plan?canceled=true"
}
```

### 2. create-portal-session.ts
**功能**: 创建Stripe客户门户会话
- 验证用户身份和权限
- 生成客户门户URL

**请求示例**:
```json
{
  "customerId": "cus_xxxxxxxxxxxxx",
  "returnUrl": "https://yourdomain.com/profile"
}
```

### 3. get-subscription.ts
**功能**: 获取用户当前订阅
- 返回订阅状态、计划信息
- 同步Stripe最新状态

**响应示例**:
```json
{
  "success": true,
  "data": {
    "subscriptionId": "sub_xxxxxxxxxxxxx",
    "status": "active",
    "planId": "professional",
    "planName": "Professional",
    "currentPeriodEnd": 1640995200
  }
}
```

### 4. stripe-webhook.ts
**功能**: 处理Stripe webhook事件
- 验证webhook签名
- 处理订阅创建、更新、取消事件
- 同步订阅状态到数据库

**支持的事件**:
- `checkout.session.completed`
- `customer.subscription.created`
- `customer.subscription.updated`
- `customer.subscription.deleted`
- `invoice.payment_succeeded`
- `invoice.payment_failed`

### 5. get-user-profile.ts
**功能**: 获取用户完整资料
- 用户基本信息
- 订阅状态
- 使用统计
- 计划限制

### 6. update-usage.ts
**功能**: 更新用户使用统计
- 检查使用限制
- 更新搜索次数和API调用次数
- 返回剩余配额

## 🗄 数据库设计

### Users表
```typescript
{
  userId: string;          // 主键，来自Cognito
  email: string;
  name?: string;
  stripeCustomerId?: string;
  plan: 'free' | 'professional' | 'enterprise';
  emailVerified: boolean;
  createdAt: string;
  updatedAt: string;
}
```

### Subscriptions表
```typescript
{
  userId: string;          // 分区键
  subscriptionId: string;  // 排序键
  stripeSubscriptionId: string;
  stripeCustomerId: string;
  status: SubscriptionStatus;
  planId: string;
  planName: string;
  priceId: string;
  currentPeriodStart: number;
  currentPeriodEnd: number;
  cancelAtPeriodEnd: boolean;
  trialEnd?: number;
  createdAt: string;
  updatedAt: string;
}
```

### Usage表
```typescript
{
  userId: string;    // 分区键
  date: string;      // 排序键 (YYYY-MM-DD)
  searches: number;
  apiCalls: number;
  lastUpdated: string;
}
```

## 🔒 安全考虑

### 身份验证
- 所有API端点都需要JWT token验证
- Token从Authorization header中提取
- 支持"Bearer token"和直接token格式

### 权限控制
- 用户只能访问自己的资源
- Stripe客户ID验证
- 订阅权限检查

### Webhook安全
- Stripe webhook签名验证
- 防重放攻击
- 错误处理和日志记录

## 📊 监控和日志

### CloudWatch日志
- 所有函数都有详细的日志记录
- 错误信息包含上下文
- 性能指标监控

### 错误处理
- 统一的错误响应格式
- 适当的HTTP状态码
- 用户友好的错误消息

## 🧪 测试

### 单元测试
```bash
npm test
```

### 集成测试
```bash
# 使用Stripe测试卡号
# 4242 4242 4242 4242 (Visa)
# 4000 0000 0000 0002 (需要验证)
```

## 🚀 部署流程

### 1. 准备工作
- 配置AWS CLI
- 设置Stripe账户和产品
- 验证SES邮箱地址

### 2. 部署命令
```bash
# 开发环境
serverless deploy --stage dev

# 生产环境
serverless deploy --stage prod
```

### 3. 部署后配置
- 更新前端环境变量中的API_URL
- 配置Stripe webhook端点
- 测试所有API端点

## 🔧 故障排除

### 常见问题

1. **Stripe webhook验证失败**
   - 检查webhook密钥是否正确
   - 确认端点URL正确
   - 查看CloudWatch日志

2. **DynamoDB权限错误**
   - 检查IAM角色权限
   - 确认表名配置正确

3. **JWT验证失败**
   - 检查JWT密钥配置
   - 确认token格式正确
   - 检查token过期时间

### 日志查看
```bash
# 查看函数日志
serverless logs -f createCheckoutSession --tail

# 查看特定时间段的日志
serverless logs -f stripeWebhook --startTime 1h
```

## 📈 性能优化

### 冷启动优化
- 最小化依赖包大小
- 使用连接池
- 预热关键函数

### 数据库优化
- 合理的分区键设计
- 使用GSI进行查询优化
- 设置TTL清理过期数据

## 🔄 版本管理

### 部署版本
- 使用Git标签管理版本
- 蓝绿部署策略
- 回滚机制

### 数据库迁移
- 向后兼容的schema变更
- 数据迁移脚本
- 备份和恢复策略

这个Lambda函数集合提供了完整的SaaS订阅管理功能，支持从用户注册到付费订阅的完整流程。

## 第一次部署
1. 进入项目目录
cd /Users/<USER>/Documents/knownsec/project/aws-gcandle/lambda

2. 安装依赖
npm install

3. 安装Serverless Framework
npm install -g serverless

4. 配置AWS凭证
aws configure

5. 配置环境变量
cp .env.example .env
编辑 .env 文件

6. 检查配置
make check-env

7. 部署到开发环境
make deploy-dev

## 常用部署命令
### 查看所有可用命令
make help

### 部署相关
make deploy-dev     # 部署到开发环境
make deploy-staging # 部署到测试环境  
make deploy-prod    # 部署到生产环境

### 监控和调试
make logs          # 查看函数日志
make info          # 查看部署信息
make monitor       # 打开CloudWatch监控

### 维护
make clean         # 清理构建文件
make remove        # 删除部署 (谨慎使用)