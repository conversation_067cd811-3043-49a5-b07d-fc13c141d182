{"/Users/<USER>/Documents/knownsec/project/aws-gcandle/lambda/serverless.yml": {"versionFramework": "4.17.0", "servicePath": "/Users/<USER>/Documents/knownsec/project/aws-gcandle/lambda/serverless.yml", "serviceConfigFileName": "serverless.yml", "service": {"service": "gcandle-saas-api", "frameworkVersion": "4", "useDotenv": true, "provider": {"name": "aws", "runtime": "nodejs18.x", "region": "us-east-1", "stage": "dev", "memorySize": 256, "timeout": 30, "deploymentMethod": "direct", "environment": {"STAGE": "dev", "REGION": "us-east-1", "STRIPE_SECRET_KEY": "<REDACTED>", "STRIPE_WEBHOOK_SECRET": "<REDACTED>", "STRIPE_PROFESSIONAL_MONTHLY_PRICE_ID": "price_1RYimHQwhClxmCRN2tCSC1Vo", "STRIPE_PROFESSIONAL_ANNUAL_PRICE_ID": "price_xxxxxxxxxxxxxxxxxxxxx", "STRIPE_ENTERPRISE_MONTHLY_PRICE_ID": "price_1RYimZQwhClxmCRNCaNcaGR0", "STRIPE_ENTERPRISE_ANNUAL_PRICE_ID": "price_xxxxxxxxxxxxxxxxxxxxx", "DYNAMODB_USERS_TABLE": "gcandle-users-dev", "DYNAMODB_SUBSCRIPTIONS_TABLE": "gcandle-subscriptions-dev", "DYNAMODB_USAGE_TABLE": "gcandle-usage-dev", "DYNAMODB_INVOICES_TABLE": "gcandle-invoices-dev", "COGNITO_USER_POOL_ID": "ap-southeast-2_5QO5mwLx5", "JWT_SECRET": "<REDACTED>", "FRONTEND_URL": "http://localhost:9001", "API_URL": "https://your-api-gateway-id.execute-api.us-east-1.amazonaws.com/prod"}, "iam": {"role": {"statements": [{"Effect": "Allow", "Action": ["dynamodb:Query", "dynamodb:<PERSON><PERSON>", "dynamodb:GetItem", "dynamodb:PutItem", "dynamodb:UpdateItem", "dynamodb:DeleteItem"], "Resource": ["arn:aws:dynamodb:us-east-1:*:table/gcandle-users-dev", "arn:aws:dynamodb:us-east-1:*:table/gcandle-subscriptions-dev", "arn:aws:dynamodb:us-east-1:*:table/gcandle-usage-dev", "arn:aws:dynamodb:us-east-1:*:table/gcandle-invoices-dev"]}, {"Effect": "Allow", "Action": ["ses:SendEmail", "ses:SendRawEmail"], "Resource": "*"}]}}, "versionFunctions": true}, "custom": {"tables": {"users": "gcandle-users-dev", "subscriptions": "gcandle-subscriptions-dev", "usage": "gcandle-usage-dev", "invoices": "gcandle-invoices-dev"}}, "functions": {"createCheckoutSession": {"handler": "dist/functions/create-checkout-session.handler", "events": [{"http": {"path": "stripe/create-checkout-session", "method": "post", "cors": true}}], "name": "gcandle-saas-api-dev-createCheckoutSession"}, "createPortalSession": {"handler": "dist/functions/create-portal-session.handler", "events": [{"http": {"path": "stripe/create-portal-session", "method": "post", "cors": true}}], "name": "gcandle-saas-api-dev-createPortalSession"}, "getSubscription": {"handler": "dist/functions/get-subscription.handler", "events": [{"http": {"path": "stripe/subscription", "method": "get", "cors": true}}], "name": "gcandle-saas-api-dev-getSubscription"}, "cancelSubscription": {"handler": "dist/functions/cancel-subscription.handler", "events": [{"http": {"path": "stripe/subscription/{subscriptionId}/cancel", "method": "post", "cors": true}}], "name": "gcandle-saas-api-dev-cancelSubscription"}, "stripeWebhook": {"handler": "dist/functions/stripe-webhook.handler", "events": [{"http": {"path": "stripe/webhook", "method": "post", "cors": false}}], "name": "gcandle-saas-api-dev-stripeWebhook"}, "getUserProfile": {"handler": "dist/functions/get-user-profile.handler", "events": [{"http": {"path": "user/profile", "method": "get", "cors": true}}], "name": "gcandle-saas-api-dev-getUserProfile"}, "updateUsage": {"handler": "dist/functions/update-usage.handler", "events": [{"http": {"path": "user/usage", "method": "post", "cors": true}}], "name": "gcandle-saas-api-dev-updateUsage"}}, "resources": {"Resources": {"UsersTable": {"Type": "AWS::DynamoDB::Table", "Properties": {"TableName": "gcandle-users-dev", "BillingMode": "PAY_PER_REQUEST", "AttributeDefinitions": [{"AttributeName": "userId", "AttributeType": "S"}], "KeySchema": [{"AttributeName": "userId", "KeyType": "HASH"}], "StreamSpecification": {"StreamViewType": "NEW_AND_OLD_IMAGES"}, "PointInTimeRecoverySpecification": {"PointInTimeRecoveryEnabled": true}, "Tags": [{"Key": "Environment", "Value": "dev"}, {"Key": "Service", "Value": "gcandle-saas"}]}}, "SubscriptionsTable": {"Type": "AWS::DynamoDB::Table", "Properties": {"TableName": "gcandle-subscriptions-dev", "BillingMode": "PAY_PER_REQUEST", "AttributeDefinitions": [{"AttributeName": "userId", "AttributeType": "S"}, {"AttributeName": "subscriptionId", "AttributeType": "S"}], "KeySchema": [{"AttributeName": "userId", "KeyType": "HASH"}, {"AttributeName": "subscriptionId", "KeyType": "RANGE"}], "StreamSpecification": {"StreamViewType": "NEW_AND_OLD_IMAGES"}, "PointInTimeRecoverySpecification": {"PointInTimeRecoveryEnabled": true}, "Tags": [{"Key": "Environment", "Value": "dev"}, {"Key": "Service", "Value": "gcandle-saas"}]}}, "UsageTable": {"Type": "AWS::DynamoDB::Table", "Properties": {"TableName": "gcandle-usage-dev", "BillingMode": "PAY_PER_REQUEST", "AttributeDefinitions": [{"AttributeName": "userId", "AttributeType": "S"}, {"AttributeName": "date", "AttributeType": "S"}], "KeySchema": [{"AttributeName": "userId", "KeyType": "HASH"}, {"AttributeName": "date", "KeyType": "RANGE"}], "TimeToLiveSpecification": {"AttributeName": "ttl", "Enabled": true}, "Tags": [{"Key": "Environment", "Value": "dev"}, {"Key": "Service", "Value": "gcandle-saas"}]}}, "InvoicesTable": {"Type": "AWS::DynamoDB::Table", "Properties": {"TableName": "gcandle-invoices-dev", "BillingMode": "PAY_PER_REQUEST", "AttributeDefinitions": [{"AttributeName": "invoiceId", "AttributeType": "S"}, {"AttributeName": "userId", "AttributeType": "S"}], "KeySchema": [{"AttributeName": "invoiceId", "KeyType": "HASH"}], "GlobalSecondaryIndexes": [{"IndexName": "UserInvoicesIndex", "KeySchema": [{"AttributeName": "userId", "KeyType": "HASH"}], "Projection": {"ProjectionType": "ALL"}}], "Tags": [{"Key": "Environment", "Value": "dev"}, {"Key": "Service", "Value": "gcandle-saas"}]}}}, "Outputs": {"ApiGatewayRestApiId": {"Value": {"Ref": "ApiGatewayRestApi"}, "Export": {"Name": "gcandle-saas-api-dev-restApiId"}}, "ApiGatewayRestApiRootResourceId": {"Value": {"Fn::GetAtt": ["ApiGatewayRestApi", "RootResourceId"]}, "Export": {"Name": "gcandle-saas-api-dev-rootResourceId"}}, "ApiEndpoint": {"Value": {"Fn::Join": ["", ["https://", {"Ref": "ApiGatewayRestApi"}, ".execute-api.", "us-east-1", ".amazonaws.com/", "dev"]]}, "Export": {"Name": "gcandle-saas-api-dev-apiEndpoint"}}}}, "plugins": ["serverless-offline"]}, "provider": {"name": "aws", "runtime": "nodejs18.x", "region": "us-east-1", "stage": "dev", "memorySize": 256, "timeout": 30, "deploymentMethod": "direct", "environment": {"STAGE": "dev", "REGION": "us-east-1", "STRIPE_SECRET_KEY": "<REDACTED>", "STRIPE_WEBHOOK_SECRET": "<REDACTED>", "STRIPE_PROFESSIONAL_MONTHLY_PRICE_ID": "price_1RYimHQwhClxmCRN2tCSC1Vo", "STRIPE_PROFESSIONAL_ANNUAL_PRICE_ID": "price_xxxxxxxxxxxxxxxxxxxxx", "STRIPE_ENTERPRISE_MONTHLY_PRICE_ID": "price_1RYimZQwhClxmCRNCaNcaGR0", "STRIPE_ENTERPRISE_ANNUAL_PRICE_ID": "price_xxxxxxxxxxxxxxxxxxxxx", "DYNAMODB_USERS_TABLE": "gcandle-users-dev", "DYNAMODB_SUBSCRIPTIONS_TABLE": "gcandle-subscriptions-dev", "DYNAMODB_USAGE_TABLE": "gcandle-usage-dev", "DYNAMODB_INVOICES_TABLE": "gcandle-invoices-dev", "COGNITO_USER_POOL_ID": "ap-southeast-2_5QO5mwLx5", "JWT_SECRET": "<REDACTED>", "FRONTEND_URL": "http://localhost:9001", "API_URL": "https://your-api-gateway-id.execute-api.us-east-1.amazonaws.com/prod"}, "iam": {"role": {"statements": [{"Effect": "Allow", "Action": ["dynamodb:Query", "dynamodb:<PERSON><PERSON>", "dynamodb:GetItem", "dynamodb:PutItem", "dynamodb:UpdateItem", "dynamodb:DeleteItem"], "Resource": ["arn:aws:dynamodb:us-east-1:*:table/gcandle-users-dev", "arn:aws:dynamodb:us-east-1:*:table/gcandle-subscriptions-dev", "arn:aws:dynamodb:us-east-1:*:table/gcandle-usage-dev", "arn:aws:dynamodb:us-east-1:*:table/gcandle-invoices-dev"]}, {"Effect": "Allow", "Action": ["ses:SendEmail", "ses:SendRawEmail"], "Resource": "*"}]}}, "versionFunctions": true}, "dashboard": {"isEnabledForService": false, "requiredAuthentication": false, "orgFeaturesInUse": null, "orgObservabilityIntegrations": null, "serviceAppId": null, "serviceProvider": null, "instanceParameters": null}, "error": {"message": "connect ETIMEDOUT ***************:443", "stack": "Error: connect ETIMEDOUT ***************:443\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1634:16)", "code": "ETIMEDOUT"}, "serviceRawFile": "service: gcandle-saas-api\n\nframeworkVersion: '4'\n\nuseDotenv: true\n\nprovider:\n  name: aws\n  runtime: nodejs18.x\n  region: us-east-1\n  stage: ${opt:stage, 'dev'}\n  memorySize: 256\n  timeout: 30\n  deploymentMethod: direct\n  \n  environment:\n    STAGE: ${self:provider.stage}\n    REGION: ${self:provider.region}\n    \n    # Stripe Configuration\n    STRIPE_SECRET_KEY: ${env:STRIPE_SECRET_KEY}\n    STRIPE_WEBHOOK_SECRET: ${env:STRIPE_WEBHOOK_SECRET}\n    STRIPE_PROFESSIONAL_MONTHLY_PRICE_ID: ${env:STRIPE_PROFESSIONAL_MONTHLY_PRICE_ID}\n    STRIPE_PROFESSIONAL_ANNUAL_PRICE_ID: ${env:STRIPE_PROFESSIONAL_ANNUAL_PRICE_ID}\n    STRIPE_ENTERPRISE_MONTHLY_PRICE_ID: ${env:STRIPE_ENTERPRISE_MONTHLY_PRICE_ID}\n    STRIPE_ENTERPRISE_ANNUAL_PRICE_ID: ${env:STRIPE_ENTERPRISE_ANNUAL_PRICE_ID}\n    \n    # DynamoDB Tables\n    DYNAMODB_USERS_TABLE: ${self:custom.tables.users}\n    DYNAMODB_SUBSCRIPTIONS_TABLE: ${self:custom.tables.subscriptions}\n    DYNAMODB_USAGE_TABLE: ${self:custom.tables.usage}\n    DYNAMODB_INVOICES_TABLE: ${self:custom.tables.invoices}\n    \n    # AWS Cognito\n    COGNITO_USER_POOL_ID: ${env:COGNITO_USER_POOL_ID}\n    \n    # SES Configuration\n    # SES_FROM_EMAIL: ${env:SES_FROM_EMAIL}\n    # SES_REGION: ${self:provider.region}\n    \n    # JWT Secret\n    JWT_SECRET: ${env:JWT_SECRET}\n    \n    # Application URLs\n    FRONTEND_URL: ${env:FRONTEND_URL}\n    API_URL: ${env:API_URL}\n\n  iam:\n    role:\n      statements:\n        - Effect: Allow\n          Action:\n            - dynamodb:Query\n            - dynamodb:Scan\n            - dynamodb:GetItem\n            - dynamodb:PutItem\n            - dynamodb:UpdateItem\n            - dynamodb:DeleteItem\n          Resource:\n            - \"arn:aws:dynamodb:${self:provider.region}:*:table/${self:custom.tables.users}\"\n            - \"arn:aws:dynamodb:${self:provider.region}:*:table/${self:custom.tables.subscriptions}\"\n            - \"arn:aws:dynamodb:${self:provider.region}:*:table/${self:custom.tables.usage}\"\n            - \"arn:aws:dynamodb:${self:provider.region}:*:table/${self:custom.tables.invoices}\"\n        - Effect: Allow\n          Action:\n            - ses:SendEmail\n            - ses:SendRawEmail\n          Resource: \"*\"\n\ncustom:\n  tables:\n    users: gcandle-users-${self:provider.stage}\n    subscriptions: gcandle-subscriptions-${self:provider.stage}\n    usage: gcandle-usage-${self:provider.stage}\n    invoices: gcandle-invoices-${self:provider.stage}\n\nfunctions:\n  # Stripe Functions\n  createCheckoutSession:\n    handler: dist/functions/create-checkout-session.handler\n    events:\n      - http:\n          path: stripe/create-checkout-session\n          method: post\n          cors: true\n          \n  createPortalSession:\n    handler: dist/functions/create-portal-session.handler\n    events:\n      - http:\n          path: stripe/create-portal-session\n          method: post\n          cors: true\n          \n  getSubscription:\n    handler: dist/functions/get-subscription.handler\n    events:\n      - http:\n          path: stripe/subscription\n          method: get\n          cors: true\n          \n  cancelSubscription:\n    handler: dist/functions/cancel-subscription.handler\n    events:\n      - http:\n          path: stripe/subscription/{subscriptionId}/cancel\n          method: post\n          cors: true\n          \n  stripeWebhook:\n    handler: dist/functions/stripe-webhook.handler\n    events:\n      - http:\n          path: stripe/webhook\n          method: post\n          cors: false # Webhooks don't need CORS\n          \n  # User Functions\n  getUserProfile:\n    handler: dist/functions/get-user-profile.handler\n    events:\n      - http:\n          path: user/profile\n          method: get\n          cors: true\n          \n  updateUsage:\n    handler: dist/functions/update-usage.handler\n    events:\n      - http:\n          path: user/usage\n          method: post\n          cors: true\n\nresources:\n  Resources:\n    # Users Table\n    UsersTable:\n      Type: AWS::DynamoDB::Table\n      Properties:\n        TableName: ${self:custom.tables.users}\n        BillingMode: PAY_PER_REQUEST\n        AttributeDefinitions:\n          - AttributeName: userId\n            AttributeType: S\n        KeySchema:\n          - AttributeName: userId\n            KeyType: HASH\n        StreamSpecification:\n          StreamViewType: NEW_AND_OLD_IMAGES\n        PointInTimeRecoverySpecification:\n          PointInTimeRecoveryEnabled: true\n        Tags:\n          - Key: Environment\n            Value: ${self:provider.stage}\n          - Key: Service\n            Value: gcandle-saas\n            \n    # Subscriptions Table\n    SubscriptionsTable:\n      Type: AWS::DynamoDB::Table\n      Properties:\n        TableName: ${self:custom.tables.subscriptions}\n        BillingMode: PAY_PER_REQUEST\n        AttributeDefinitions:\n          - AttributeName: userId\n            AttributeType: S\n          - AttributeName: subscriptionId\n            AttributeType: S\n        KeySchema:\n          - AttributeName: userId\n            KeyType: HASH\n          - AttributeName: subscriptionId\n            KeyType: RANGE\n        StreamSpecification:\n          StreamViewType: NEW_AND_OLD_IMAGES\n        PointInTimeRecoverySpecification:\n          PointInTimeRecoveryEnabled: true\n        Tags:\n          - Key: Environment\n            Value: ${self:provider.stage}\n          - Key: Service\n            Value: gcandle-saas\n            \n    # Usage Table\n    UsageTable:\n      Type: AWS::DynamoDB::Table\n      Properties:\n        TableName: ${self:custom.tables.usage}\n        BillingMode: PAY_PER_REQUEST\n        AttributeDefinitions:\n          - AttributeName: userId\n            AttributeType: S\n          - AttributeName: date\n            AttributeType: S\n        KeySchema:\n          - AttributeName: userId\n            KeyType: HASH\n          - AttributeName: date\n            KeyType: RANGE\n        TimeToLiveSpecification:\n          AttributeName: ttl\n          Enabled: true\n        Tags:\n          - Key: Environment\n            Value: ${self:provider.stage}\n          - Key: Service\n            Value: gcandle-saas\n            \n    # Invoices Table\n    InvoicesTable:\n      Type: AWS::DynamoDB::Table\n      Properties:\n        TableName: ${self:custom.tables.invoices}\n        BillingMode: PAY_PER_REQUEST\n        AttributeDefinitions:\n          - AttributeName: invoiceId\n            AttributeType: S\n          - AttributeName: userId\n            AttributeType: S\n        KeySchema:\n          - AttributeName: invoiceId\n            KeyType: HASH\n        GlobalSecondaryIndexes:\n          - IndexName: UserInvoicesIndex\n            KeySchema:\n              - AttributeName: userId\n                KeyType: HASH\n            Projection:\n              ProjectionType: ALL\n        Tags:\n          - Key: Environment\n            Value: ${self:provider.stage}\n          - Key: Service\n            Value: gcandle-saas\n\n  Outputs:\n    ApiGatewayRestApiId:\n      Value:\n        Ref: ApiGatewayRestApi\n      Export:\n        Name: ${self:service}-${self:provider.stage}-restApiId\n        \n    ApiGatewayRestApiRootResourceId:\n      Value:\n        Fn::GetAtt:\n          - ApiGatewayRestApi\n          - RootResourceId\n      Export:\n        Name: ${self:service}-${self:provider.stage}-rootResourceId\n        \n    ApiEndpoint:\n      Value:\n        Fn::Join:\n          - ''\n          - - 'https://'\n            - Ref: ApiGatewayRestApi\n            - '.execute-api.'\n            - ${self:provider.region}\n            - '.amazonaws.com/'\n            - ${self:provider.stage}\n      Export:\n        Name: ${self:service}-${self:provider.stage}-apiEndpoint\n\nplugins:\n  - serverless-offline\n", "command": ["deploy"], "options": {"stage": "dev", "region": "us-east-1", "verbose": true, "debug": "*"}, "orgId": "89dc85ac-cab4-4067-b79b-9017090c3d64", "orgName": "luoyunfu", "userId": "KZVkJphprJWGj9dbGX", "userName": "luoyunfu", "serviceProviderAwsAccountId": "************", "serviceProviderAwsCfStackId": null, "serviceProviderAwsCfStackCreated": null, "serviceProviderAwsCfStackUpdated": null, "serviceProviderAwsCfStackStatus": null, "serviceProviderAwsCfStackOutputs": null}, "/Users/<USER>/Documents/knownsec/project/aws-gcandle/lambda/template.yaml": {"versionFramework": "4.17.1", "servicePath": "/Users/<USER>/Documents/knownsec/project/aws-gcandle/lambda/template.yaml", "serviceConfigFileName": "template.yaml", "service": {"AWSTemplateFormatVersion": "2010-09-09", "Transform": "AWS::Serverless-2016-10-31", "Description": "Gcandle SaaS API Lambda Functions", "Parameters": {"Stage": {"Type": "String", "Default": "dev", "AllowedValues": ["dev", "staging", "prod"]}, "StripeSecretKey": "<REDACTED>", "StripeWebhookSecret": "<REDACTED>", "CognitoUserPoolId": {"Type": "String", "Description": "Cognito User Pool ID"}, "JwtSecret": "<REDACTED>", "FrontendUrl": {"Type": "String", "Description": "Frontend URL"}}, "Globals": {"Function": {"Runtime": "nodejs18.x", "Timeout": 30, "MemorySize": 256, "Environment": {"Variables": {"STAGE": {"Ref": "Stage"}, "REGION": {"Ref": "AWS::Region"}, "STRIPE_SECRET_KEY": "<REDACTED>", "STRIPE_WEBHOOK_SECRET": "<REDACTED>", "COGNITO_USER_POOL_ID": {"Ref": "CognitoUserPoolId"}, "JWT_SECRET": "<REDACTED>", "FRONTEND_URL": {"Ref": "FrontendUrl"}, "DYNAMODB_USERS_TABLE": {"Ref": "UsersTable"}, "DYNAMODB_SUBSCRIPTIONS_TABLE": {"Ref": "SubscriptionsTable"}, "DYNAMODB_USAGE_TABLE": {"Ref": "UsageTable"}, "DYNAMODB_INVOICES_TABLE": {"Ref": "InvoicesTable"}}}}}, "Resources": {"ApiGateway": {"Type": "AWS::Serverless::Api", "Properties": {"StageName": {"Ref": "Stage"}, "Cors": {"AllowMethods": "'GET,POST,PUT,DELETE,OPTIONS'", "AllowHeaders": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'", "AllowOrigin": "'*'"}}}, "CreateCheckoutSessionFunction": {"Type": "AWS::Serverless::Function", "Properties": {"CodeUri": "dist/", "Handler": "functions/create-checkout-session.handler", "Events": {"Api": {"Type": "Api", "Properties": {"RestApiId": {"Ref": "ApiGateway"}, "Path": "/stripe/create-checkout-session", "Method": "post"}}}}}, "CreatePortalSessionFunction": {"Type": "AWS::Serverless::Function", "Properties": {"CodeUri": "dist/", "Handler": "functions/create-portal-session.handler", "Events": {"Api": {"Type": "Api", "Properties": {"RestApiId": {"Ref": "ApiGateway"}, "Path": "/stripe/create-portal-session", "Method": "post"}}}}}, "GetSubscriptionFunction": {"Type": "AWS::Serverless::Function", "Properties": {"CodeUri": "dist/", "Handler": "functions/get-subscription.handler", "Events": {"Api": {"Type": "Api", "Properties": {"RestApiId": {"Ref": "ApiGateway"}, "Path": "/stripe/subscription", "Method": "get"}}}}}, "CancelSubscriptionFunction": {"Type": "AWS::Serverless::Function", "Properties": {"CodeUri": "dist/", "Handler": "functions/cancel-subscription.handler", "Events": {"Api": {"Type": "Api", "Properties": {"RestApiId": {"Ref": "ApiGateway"}, "Path": "/stripe/subscription/{subscriptionId}/cancel", "Method": "post"}}}}}, "StripeWebhookFunction": {"Type": "AWS::Serverless::Function", "Properties": {"CodeUri": "dist/", "Handler": "functions/stripe-webhook.handler", "Events": {"Api": {"Type": "Api", "Properties": {"RestApiId": {"Ref": "ApiGateway"}, "Path": "/stripe/webhook", "Method": "post"}}}}}, "GetUserProfileFunction": {"Type": "AWS::Serverless::Function", "Properties": {"CodeUri": "dist/", "Handler": "functions/get-user-profile.handler", "Events": {"Api": {"Type": "Api", "Properties": {"RestApiId": {"Ref": "ApiGateway"}, "Path": "/user/profile", "Method": "get"}}}}}, "UpdateUsageFunction": {"Type": "AWS::Serverless::Function", "Properties": {"CodeUri": "dist/", "Handler": "functions/update-usage.handler", "Events": {"Api": {"Type": "Api", "Properties": {"RestApiId": {"Ref": "ApiGateway"}, "Path": "/user/usage", "Method": "post"}}}}}, "UsersTable": {"Type": "AWS::DynamoDB::Table", "Properties": {"TableName": {"Fn::Sub": "gcandle-users-${Stage}"}, "BillingMode": "PAY_PER_REQUEST", "AttributeDefinitions": [{"AttributeName": "userId", "AttributeType": "S"}], "KeySchema": [{"AttributeName": "userId", "KeyType": "HASH"}], "StreamSpecification": {"StreamViewType": "NEW_AND_OLD_IMAGES"}, "PointInTimeRecoverySpecification": {"PointInTimeRecoveryEnabled": true}}}, "SubscriptionsTable": {"Type": "AWS::DynamoDB::Table", "Properties": {"TableName": {"Fn::Sub": "gcandle-subscriptions-${Stage}"}, "BillingMode": "PAY_PER_REQUEST", "AttributeDefinitions": [{"AttributeName": "userId", "AttributeType": "S"}, {"AttributeName": "subscriptionId", "AttributeType": "S"}], "KeySchema": [{"AttributeName": "userId", "KeyType": "HASH"}, {"AttributeName": "subscriptionId", "KeyType": "RANGE"}], "StreamSpecification": {"StreamViewType": "NEW_AND_OLD_IMAGES"}, "PointInTimeRecoverySpecification": {"PointInTimeRecoveryEnabled": true}}}, "UsageTable": {"Type": "AWS::DynamoDB::Table", "Properties": {"TableName": {"Fn::Sub": "gcandle-usage-${Stage}"}, "BillingMode": "PAY_PER_REQUEST", "AttributeDefinitions": [{"AttributeName": "userId", "AttributeType": "S"}, {"AttributeName": "date", "AttributeType": "S"}], "KeySchema": [{"AttributeName": "userId", "KeyType": "HASH"}, {"AttributeName": "date", "KeyType": "RANGE"}], "TimeToLiveSpecification": {"AttributeName": "ttl", "Enabled": true}}}, "InvoicesTable": {"Type": "AWS::DynamoDB::Table", "Properties": {"TableName": {"Fn::Sub": "gcandle-invoices-${Stage}"}, "BillingMode": "PAY_PER_REQUEST", "AttributeDefinitions": [{"AttributeName": "invoiceId", "AttributeType": "S"}, {"AttributeName": "userId", "AttributeType": "S"}], "KeySchema": [{"AttributeName": "invoiceId", "KeyType": "HASH"}], "GlobalSecondaryIndexes": [{"IndexName": "UserInvoicesIndex", "KeySchema": [{"AttributeName": "userId", "KeyType": "HASH"}], "Projection": {"ProjectionType": "ALL"}}]}}, "LambdaExecutionRole": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}, "Action": "sts:<PERSON><PERSON>Role"}]}, "ManagedPolicyArns": ["arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"], "Policies": [{"PolicyName": "DynamoDBAccess", "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["dynamodb:Query", "dynamodb:<PERSON><PERSON>", "dynamodb:GetItem", "dynamodb:PutItem", "dynamodb:UpdateItem", "dynamodb:DeleteItem"], "Resource": [{"Fn::GetAtt": ["UsersTable", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["SubscriptionsTable", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["UsageTable", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["InvoicesTable", "<PERSON><PERSON>"]}]}]}}, {"PolicyName": "SESAccess", "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["ses:SendEmail", "ses:SendRawEmail"], "Resource": "*"}]}}]}}}, "Outputs": {"ApiGatewayUrl": {"Description": "API Gateway URL", "Value": {"Fn::Sub": "https://${ApiGateway}.execute-api.${AWS::Region}.amazonaws.com/${Stage}"}, "Export": {"Name": {"Fn::Sub": "${AWS::StackName}-ApiUrl"}}}, "UsersTableName": {"Description": "Users DynamoDB Table Name", "Value": {"Ref": "UsersTable"}, "Export": {"Name": {"Fn::Sub": "${AWS::StackName}-UsersTable"}}}}}, "dashboard": {"isEnabledForService": false, "requiredAuthentication": false, "orgFeaturesInUse": null, "orgObservabilityIntegrations": null, "serviceAppId": null, "serviceProvider": null, "instanceParameters": null}, "error": {"message": "Please specify a stack name using the --stack option, or persist it in the samconfig.toml file.", "stack": "Error: Please specify a stack name using the --stack option, or persist it in the samconfig.toml file.\n    at getCfnConfig (/Users/<USER>/.serverless/releases/4.17.1/sf-core/src/lib/runners/cfn/utils.js:106:11)\n    at deploy_default (/Users/<USER>/.serverless/releases/4.17.1/sf-core/src/lib/runners/cfn/commands/deploy.js:45:21)\n    at CfnRunner.run (/Users/<USER>/.serverless/releases/4.17.1/sf-core/src/lib/runners/cfn/cfn.js:168:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async route (/Users/<USER>/.serverless/releases/4.17.1/sf-core/src/lib/router.js:103:20)\n    at async Object.run (/Users/<USER>/.serverless/releases/4.17.1/sf-core/src/index.js:39:3)\n    at async run2 (/Users/<USER>/.serverless/releases/4.17.1/sf-core/bin/sf-core.js:31:3)", "code": "MISSING_STACK_NAME"}, "serviceRawFile": "AWSTemplateFormatVersion: '2010-09-09'\nTransform: AWS::Serverless-2016-10-31\nDescription: Gcandle SaaS API Lambda Functions\n\nParameters:\n  Stage:\n    Type: String\n    Default: dev\n    AllowedValues: [dev, staging, prod]\n  \n  StripeSecretKey:\n    Type: String\n    NoEcho: true\n    Description: Stripe Secret Key\n  \n  StripeWebhookSecret:\n    Type: String\n    NoEcho: true\n    Description: Stripe Webhook Secret\n  \n  CognitoUserPoolId:\n    Type: String\n    Description: Cognito User Pool ID\n  \n  JwtSecret:\n    Type: String\n    NoEcho: true\n    Description: JWT Secret Key\n  \n  FrontendUrl:\n    Type: String\n    Description: Frontend URL\n\nGlobals:\n  Function:\n    Runtime: nodejs18.x\n    Timeout: 30\n    MemorySize: 256\n    Environment:\n      Variables:\n        STAGE: !Ref Stage\n        REGION: !Ref AWS::Region\n        STRIPE_SECRET_KEY: !Ref StripeSecretKey\n        STRIPE_WEBHOOK_SECRET: !Ref StripeWebhookSecret\n        COGNITO_USER_POOL_ID: !Ref CognitoUserPoolId\n        JWT_SECRET: !Ref JwtSecret\n        FRONTEND_URL: !Ref FrontendUrl\n        DYNAMODB_USERS_TABLE: !Ref UsersTable\n        DYNAMODB_SUBSCRIPTIONS_TABLE: !Ref SubscriptionsTable\n        DYNAMODB_USAGE_TABLE: !Ref UsageTable\n        DYNAMODB_INVOICES_TABLE: !Ref InvoicesTable\n\nResources:\n  # API Gateway\n  ApiGateway:\n    Type: AWS::Serverless::Api\n    Properties:\n      StageName: !Ref Stage\n      Cors:\n        AllowMethods: \"'GET,POST,PUT,DELETE,OPTIONS'\"\n        AllowHeaders: \"'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'\"\n        AllowOrigin: \"'*'\"\n\n  # Lambda Functions\n  CreateCheckoutSessionFunction:\n    Type: AWS::Serverless::Function\n    Properties:\n      CodeUri: dist/\n      Handler: functions/create-checkout-session.handler\n      Events:\n        Api:\n          Type: Api\n          Properties:\n            RestApiId: !Ref ApiGateway\n            Path: /stripe/create-checkout-session\n            Method: post\n\n  CreatePortalSessionFunction:\n    Type: AWS::Serverless::Function\n    Properties:\n      CodeUri: dist/\n      Handler: functions/create-portal-session.handler\n      Events:\n        Api:\n          Type: Api\n          Properties:\n            RestApiId: !Ref ApiGateway\n            Path: /stripe/create-portal-session\n            Method: post\n\n  GetSubscriptionFunction:\n    Type: AWS::Serverless::Function\n    Properties:\n      CodeUri: dist/\n      Handler: functions/get-subscription.handler\n      Events:\n        Api:\n          Type: Api\n          Properties:\n            RestApiId: !Ref ApiGateway\n            Path: /stripe/subscription\n            Method: get\n\n  CancelSubscriptionFunction:\n    Type: AWS::Serverless::Function\n    Properties:\n      CodeUri: dist/\n      Handler: functions/cancel-subscription.handler\n      Events:\n        Api:\n          Type: Api\n          Properties:\n            RestApiId: !Ref ApiGateway\n            Path: /stripe/subscription/{subscriptionId}/cancel\n            Method: post\n\n  StripeWebhookFunction:\n    Type: AWS::Serverless::Function\n    Properties:\n      CodeUri: dist/\n      Handler: functions/stripe-webhook.handler\n      Events:\n        Api:\n          Type: Api\n          Properties:\n            RestApiId: !Ref ApiGateway\n            Path: /stripe/webhook\n            Method: post\n\n  GetUserProfileFunction:\n    Type: AWS::Serverless::Function\n    Properties:\n      CodeUri: dist/\n      Handler: functions/get-user-profile.handler\n      Events:\n        Api:\n          Type: Api\n          Properties:\n            RestApiId: !Ref ApiGateway\n            Path: /user/profile\n            Method: get\n\n  UpdateUsageFunction:\n    Type: AWS::Serverless::Function\n    Properties:\n      CodeUri: dist/\n      Handler: functions/update-usage.handler\n      Events:\n        Api:\n          Type: Api\n          Properties:\n            RestApiId: !Ref ApiGateway\n            Path: /user/usage\n            Method: post\n\n  # DynamoDB Tables\n  UsersTable:\n    Type: AWS::DynamoDB::Table\n    Properties:\n      TableName: !Sub gcandle-users-${Stage}\n      BillingMode: PAY_PER_REQUEST\n      AttributeDefinitions:\n        - AttributeName: userId\n          AttributeType: S\n      KeySchema:\n        - AttributeName: userId\n          KeyType: HASH\n      StreamSpecification:\n        StreamViewType: NEW_AND_OLD_IMAGES\n      PointInTimeRecoverySpecification:\n        PointInTimeRecoveryEnabled: true\n\n  SubscriptionsTable:\n    Type: AWS::DynamoDB::Table\n    Properties:\n      TableName: !Sub gcandle-subscriptions-${Stage}\n      BillingMode: PAY_PER_REQUEST\n      AttributeDefinitions:\n        - AttributeName: userId\n          AttributeType: S\n        - AttributeName: subscriptionId\n          AttributeType: S\n      KeySchema:\n        - AttributeName: userId\n          KeyType: HASH\n        - AttributeName: subscriptionId\n          KeyType: RANGE\n      StreamSpecification:\n        StreamViewType: NEW_AND_OLD_IMAGES\n      PointInTimeRecoverySpecification:\n        PointInTimeRecoveryEnabled: true\n\n  UsageTable:\n    Type: AWS::DynamoDB::Table\n    Properties:\n      TableName: !Sub gcandle-usage-${Stage}\n      BillingMode: PAY_PER_REQUEST\n      AttributeDefinitions:\n        - AttributeName: userId\n          AttributeType: S\n        - AttributeName: date\n          AttributeType: S\n      KeySchema:\n        - AttributeName: userId\n          KeyType: HASH\n        - AttributeName: date\n          KeyType: RANGE\n      TimeToLiveSpecification:\n        AttributeName: ttl\n        Enabled: true\n\n  InvoicesTable:\n    Type: AWS::DynamoDB::Table\n    Properties:\n      TableName: !Sub gcandle-invoices-${Stage}\n      BillingMode: PAY_PER_REQUEST\n      AttributeDefinitions:\n        - AttributeName: invoiceId\n          AttributeType: S\n        - AttributeName: userId\n          AttributeType: S\n      KeySchema:\n        - AttributeName: invoiceId\n          KeyType: HASH\n      GlobalSecondaryIndexes:\n        - IndexName: UserInvoicesIndex\n          KeySchema:\n            - AttributeName: userId\n              KeyType: HASH\n          Projection:\n            ProjectionType: ALL\n\n  # IAM Role for Lambda Functions\n  LambdaExecutionRole:\n    Type: AWS::IAM::Role\n    Properties:\n      AssumeRolePolicyDocument:\n        Version: '2012-10-17'\n        Statement:\n          - Effect: Allow\n            Principal:\n              Service: lambda.amazonaws.com\n            Action: sts:AssumeRole\n      ManagedPolicyArns:\n        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole\n      Policies:\n        - PolicyName: DynamoDBAccess\n          PolicyDocument:\n            Version: '2012-10-17'\n            Statement:\n              - Effect: Allow\n                Action:\n                  - dynamodb:Query\n                  - dynamodb:Scan\n                  - dynamodb:GetItem\n                  - dynamodb:PutItem\n                  - dynamodb:UpdateItem\n                  - dynamodb:DeleteItem\n                Resource:\n                  - !GetAtt UsersTable.Arn\n                  - !GetAtt SubscriptionsTable.Arn\n                  - !GetAtt UsageTable.Arn\n                  - !GetAtt InvoicesTable.Arn\n        - PolicyName: SESAccess\n          PolicyDocument:\n            Version: '2012-10-17'\n            Statement:\n              - Effect: Allow\n                Action:\n                  - ses:SendEmail\n                  - ses:SendRawEmail\n                Resource: '*'\n\nOutputs:\n  ApiGatewayUrl:\n    Description: API Gateway URL\n    Value: !Sub https://${ApiGateway}.execute-api.${AWS::Region}.amazonaws.com/${Stage}\n    Export:\n      Name: !Sub ${AWS::StackName}-ApiUrl\n  \n  UsersTableName:\n    Description: Users DynamoDB Table Name\n    Value: !Ref UsersTable\n    Export:\n      Name: !Sub ${AWS::StackName}-UsersTable\n", "command": ["deploy"], "options": {"stage": "dev", "region": "us-east-1", "verbose": true, "debug": "*"}, "orgId": "89dc85ac-cab4-4067-b79b-9017090c3d64", "orgName": "luoyunfu", "userId": "KZVkJphprJWGj9dbGX", "userName": "luoyunfu", "serviceProviderAwsCfStackId": null, "serviceProviderAwsCfStackCreated": null, "serviceProviderAwsCfStackUpdated": null, "serviceProviderAwsCfStackStatus": null, "serviceProviderAwsCfStackOutputs": null}}