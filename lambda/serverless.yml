service: gcandle-saas-api

frameworkVersion: '4'

useDotenv: true

provider:
  name: aws
  runtime: nodejs18.x
  region: us-east-1
  stage: ${opt:stage, 'dev'}
  memorySize: 256
  timeout: 30
  deploymentMethod: direct
  
  environment:
    STAGE: ${self:provider.stage}
    REGION: ${self:provider.region}
    
    # Stripe Configuration
    STRIPE_SECRET_KEY: ${env:STRIPE_SECRET_KEY}
    STRIPE_WEBHOOK_SECRET: ${env:STRIPE_WEBHOOK_SECRET}
    STRIPE_PROFESSIONAL_MONTHLY_PRICE_ID: ${env:STRIPE_PROFESSIONAL_MONTHLY_PRICE_ID}
    STRIPE_PROFESSIONAL_ANNUAL_PRICE_ID: ${env:STRIPE_PROFESSIONAL_ANNUAL_PRICE_ID}
    STRIPE_ENTERPRISE_MONTHLY_PRICE_ID: ${env:STRIPE_ENTERPRISE_MONTHLY_PRICE_ID}
    STRIPE_ENTERPRISE_ANNUAL_PRICE_ID: ${env:STRIPE_ENTERPRISE_ANNUAL_PRICE_ID}
    
    # DynamoDB Tables
    DYNAMODB_USERS_TABLE: ${self:custom.tables.users}
    DYNAMODB_SUBSCRIPTIONS_TABLE: ${self:custom.tables.subscriptions}
    DYNAMODB_USAGE_TABLE: ${self:custom.tables.usage}
    DYNAMODB_INVOICES_TABLE: ${self:custom.tables.invoices}
    
    # AWS Cognito
    COGNITO_USER_POOL_ID: ${env:COGNITO_USER_POOL_ID}
    
    # SES Configuration
    # SES_FROM_EMAIL: ${env:SES_FROM_EMAIL}
    # SES_REGION: ${self:provider.region}
    
    # JWT Secret
    JWT_SECRET: ${env:JWT_SECRET}
    
    # Application URLs
    FRONTEND_URL: ${env:FRONTEND_URL}
    API_URL: ${env:API_URL}

  iam:
    role:
      statements:
        - Effect: Allow
          Action:
            - dynamodb:Query
            - dynamodb:Scan
            - dynamodb:GetItem
            - dynamodb:PutItem
            - dynamodb:UpdateItem
            - dynamodb:DeleteItem
          Resource:
            - "arn:aws:dynamodb:${self:provider.region}:*:table/${self:custom.tables.users}"
            - "arn:aws:dynamodb:${self:provider.region}:*:table/${self:custom.tables.subscriptions}"
            - "arn:aws:dynamodb:${self:provider.region}:*:table/${self:custom.tables.usage}"
            - "arn:aws:dynamodb:${self:provider.region}:*:table/${self:custom.tables.invoices}"
        - Effect: Allow
          Action:
            - ses:SendEmail
            - ses:SendRawEmail
          Resource: "*"

custom:
  tables:
    users: gcandle-users-${self:provider.stage}
    subscriptions: gcandle-subscriptions-${self:provider.stage}
    usage: gcandle-usage-${self:provider.stage}
    invoices: gcandle-invoices-${self:provider.stage}

functions:
  # Stripe Functions
  createCheckoutSession:
    handler: dist/functions/create-checkout-session.handler
    events:
      - http:
          path: stripe/create-checkout-session
          method: post
          cors: true
          
  createPortalSession:
    handler: dist/functions/create-portal-session.handler
    events:
      - http:
          path: stripe/create-portal-session
          method: post
          cors: true
          
  getSubscription:
    handler: dist/functions/get-subscription.handler
    events:
      - http:
          path: stripe/subscription
          method: get
          cors: true
          
  cancelSubscription:
    handler: dist/functions/cancel-subscription.handler
    events:
      - http:
          path: stripe/subscription/{subscriptionId}/cancel
          method: post
          cors: true
          
  stripeWebhook:
    handler: dist/functions/stripe-webhook.handler
    events:
      - http:
          path: stripe/webhook
          method: post
          cors: false # Webhooks don't need CORS
          
  # User Functions
  getUserProfile:
    handler: dist/functions/get-user-profile.handler
    events:
      - http:
          path: user/profile
          method: get
          cors: true
          
  updateUsage:
    handler: dist/functions/update-usage.handler
    events:
      - http:
          path: user/usage
          method: post
          cors: true

resources:
  Resources:
    # Users Table
    UsersTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:custom.tables.users}
        BillingMode: PAY_PER_REQUEST
        AttributeDefinitions:
          - AttributeName: userId
            AttributeType: S
        KeySchema:
          - AttributeName: userId
            KeyType: HASH
        StreamSpecification:
          StreamViewType: NEW_AND_OLD_IMAGES
        PointInTimeRecoverySpecification:
          PointInTimeRecoveryEnabled: true
        Tags:
          - Key: Environment
            Value: ${self:provider.stage}
          - Key: Service
            Value: gcandle-saas
            
    # Subscriptions Table
    SubscriptionsTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:custom.tables.subscriptions}
        BillingMode: PAY_PER_REQUEST
        AttributeDefinitions:
          - AttributeName: userId
            AttributeType: S
          - AttributeName: subscriptionId
            AttributeType: S
        KeySchema:
          - AttributeName: userId
            KeyType: HASH
          - AttributeName: subscriptionId
            KeyType: RANGE
        StreamSpecification:
          StreamViewType: NEW_AND_OLD_IMAGES
        PointInTimeRecoverySpecification:
          PointInTimeRecoveryEnabled: true
        Tags:
          - Key: Environment
            Value: ${self:provider.stage}
          - Key: Service
            Value: gcandle-saas
            
    # Usage Table
    UsageTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:custom.tables.usage}
        BillingMode: PAY_PER_REQUEST
        AttributeDefinitions:
          - AttributeName: userId
            AttributeType: S
          - AttributeName: date
            AttributeType: S
        KeySchema:
          - AttributeName: userId
            KeyType: HASH
          - AttributeName: date
            KeyType: RANGE
        TimeToLiveSpecification:
          AttributeName: ttl
          Enabled: true
        Tags:
          - Key: Environment
            Value: ${self:provider.stage}
          - Key: Service
            Value: gcandle-saas
            
    # Invoices Table
    InvoicesTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:custom.tables.invoices}
        BillingMode: PAY_PER_REQUEST
        AttributeDefinitions:
          - AttributeName: invoiceId
            AttributeType: S
          - AttributeName: userId
            AttributeType: S
        KeySchema:
          - AttributeName: invoiceId
            KeyType: HASH
        GlobalSecondaryIndexes:
          - IndexName: UserInvoicesIndex
            KeySchema:
              - AttributeName: userId
                KeyType: HASH
            Projection:
              ProjectionType: ALL
        Tags:
          - Key: Environment
            Value: ${self:provider.stage}
          - Key: Service
            Value: gcandle-saas

  Outputs:
    ApiGatewayRestApiId:
      Value:
        Ref: ApiGatewayRestApi
      Export:
        Name: ${self:service}-${self:provider.stage}-restApiId
        
    ApiGatewayRestApiRootResourceId:
      Value:
        Fn::GetAtt:
          - ApiGatewayRestApi
          - RootResourceId
      Export:
        Name: ${self:service}-${self:provider.stage}-rootResourceId
        
    ApiEndpoint:
      Value:
        Fn::Join:
          - ''
          - - 'https://'
            - Ref: ApiGatewayRestApi
            - '.execute-api.'
            - ${self:provider.region}
            - '.amazonaws.com/'
            - ${self:provider.stage}
      Export:
        Name: ${self:service}-${self:provider.stage}-apiEndpoint

plugins:
  - serverless-offline
