# Lambda Environment Variables Example
# Copy this file to .env and fill in your actual values

# =============================================================================
# STRIPE CONFIGURATION
# =============================================================================

# Stripe Secret Key (starts with sk_test_ for test mode, sk_live_ for live mode)
# Get this from Stripe Dashboard > Developers > API keys
STRIPE_SECRET_KEY=sk_test_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# Stripe Webhook Secret (starts with whsec_)
# Get this from Stripe Dashboard > Developers > Webhooks > [Your webhook] > Signing secret
STRIPE_WEBHOOK_SECRET=whsec_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# Stripe Price IDs for Professional Plan
# Create these in Stripe Dashboard > Products > Add product > Add pricing
STRIPE_PROFESSIONAL_MONTHLY_PRICE_ID=price_xxxxxxxxxxxxxxxxxxxxx
STRIPE_PROFESSIONAL_ANNUAL_PRICE_ID=price_xxxxxxxxxxxxxxxxxxxxx

# Stripe Price IDs for Enterprise Plan (optional)
STRIPE_ENTERPRISE_MONTHLY_PRICE_ID=price_xxxxxxxxxxxxxxxxxxxxx
STRIPE_ENTERPRISE_ANNUAL_PRICE_ID=price_xxxxxxxxxxxxxxxxxxxxx

# =============================================================================
# AWS COGNITO CONFIGURATION
# =============================================================================

# Cognito User Pool ID (found in AWS Cognito Console)
# Format: us-east-1_xxxxxxxxx
COGNITO_USER_POOL_ID=us-east-1_xxxxxxxxx

# =============================================================================
# SES CONFIGURATION
# =============================================================================

# Email address for sending notifications (must be verified in SES)
SES_FROM_EMAIL=<EMAIL>

# =============================================================================
# JWT CONFIGURATION
# =============================================================================

# Secret key for JWT token verification (generate a strong random string)
# You can generate one with: openssl rand -base64 32
JWT_SECRET=your-super-secret-jwt-key-here-make-it-long-and-random

# =============================================================================
# APPLICATION URLS
# =============================================================================

# Frontend URL (where your Next.js app is hosted)
# Development: http://localhost:9001
# Production: https://yourdomain.com
FRONTEND_URL=https://yourdomain.com

# API URL (will be the API Gateway URL after deployment)
# This will be generated after deployment, but you can set it for local development
API_URL=https://your-api-gateway-id.execute-api.us-east-1.amazonaws.com/prod

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================

# AWS Region for deployment
AWS_REGION=us-east-1

# Deployment stage (dev, staging, prod)
STAGE=prod

# =============================================================================
# SETUP INSTRUCTIONS
# =============================================================================

# 1. Copy this file to .env
# 2. Replace all placeholder values (xxxxxxxxx) with actual values
# 3. Set up Stripe account and get API keys and price IDs
# 4. Configure AWS Cognito User Pool
# 5. Verify SES email address
# 6. Generate a strong JWT secret
# 7. Deploy using: npm run deploy
# 8. Update FRONTEND_URL environment variables with the API Gateway URL

# =============================================================================
# SECURITY NOTES
# =============================================================================

# - Never commit .env files to version control
# - Use different Stripe keys for development (test) and production (live)
# - Regularly rotate JWT secrets and API keys
# - Use AWS IAM roles with minimal required permissions
# - Enable MFA on all AWS and Stripe accounts
# - Monitor usage and set up billing alerts

# =============================================================================
# STRIPE SETUP GUIDE
# =============================================================================

# 1. Create Stripe account at https://stripe.com
# 2. Go to Dashboard > Developers > API keys
# 3. Copy the Secret key (starts with sk_)
# 4. Go to Dashboard > Products
# 5. Create "Professional Plan" product
# 6. Add monthly pricing ($99/month)
# 7. Add annual pricing ($990/year)
# 8. Copy the price IDs (start with price_)
# 9. Go to Dashboard > Developers > Webhooks
# 10. Add endpoint: https://your-api-gateway-url/prod/stripe/webhook
# 11. Select events: customer.subscription.*, invoice.payment_*
# 12. Copy the webhook signing secret (starts with whsec_)

# =============================================================================
# AWS COGNITO SETUP GUIDE
# =============================================================================

# 1. Go to AWS Cognito Console
# 2. Create User Pool or use existing one
# 3. Copy the User Pool ID (format: us-east-1_xxxxxxxxx)
# 4. Make sure your frontend is configured with the same User Pool

# =============================================================================
# SES SETUP GUIDE
# =============================================================================

# 1. Go to AWS SES Console
# 2. Verify your domain or email address
# 3. If in sandbox mode, verify recipient email addresses
# 4. For production, request production access
# 5. Use the verified email address as SES_FROM_EMAIL
