# Gcandle SaaS Lambda Functions Makefile

# Variables
STAGE ?= dev
REGION ?= us-east-1
SERVICE_NAME = gcandle-saas-api

# Colors for output
RED = \033[0;31m
GREEN = \033[0;32m
YELLOW = \033[1;33m
BLUE = \033[0;34m
NC = \033[0m # No Color

.PHONY: help install build test lint clean deploy remove logs package

# Default target
help: ## Show this help message
	@echo "$(BLUE)Gcandle SaaS Lambda Functions$(NC)"
	@echo "$(YELLOW)Available commands:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(GREEN)%-15s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

install: ## Install dependencies
	@echo "$(YELLOW)Installing dependencies...$(NC)"
	npm install
	@echo "$(GREEN)Dependencies installed successfully!$(NC)"

build: ## Build TypeScript code
	@echo "$(YELLOW)Building TypeScript code...$(NC)"
	npm run build
	@echo "$(GREEN)Build completed successfully!$(NC)"

test: ## Run tests
	@echo "$(YELLOW)Running tests...$(NC)"
	npm test
	@echo "$(GREEN)Tests completed!$(NC)"

lint: ## Run linter
	@echo "$(YELLOW)Running linter...$(NC)"
	npm run lint
	@echo "$(GREEN)Linting completed!$(NC)"

clean: ## Clean build artifacts
	@echo "$(YELLOW)Cleaning build artifacts...$(NC)"
	npm run clean
	rm -rf node_modules/.cache
	rm -rf .serverless
	@echo "$(GREEN)Clean completed!$(NC)"

package: build ## Package functions for deployment
	@echo "$(YELLOW)Packaging functions...$(NC)"
	npm run deploy
	@echo "$(GREEN)Packaging completed!$(NC)"

deploy: build ## Deploy to AWS
	@echo "$(YELLOW)Deploying to AWS (stage: $(STAGE), region: $(REGION))...$(NC)"
	@if [ ! -f .env ]; then \
		echo "$(RED)Error: .env file not found. Please copy .env.example to .env and configure it.$(NC)"; \
		exit 1; \
	fi
	./deploy-complete.sh $(STAGE) $(REGION)

deploy-dev: ## Deploy to development environment
	@$(MAKE) deploy STAGE=dev

deploy-staging: ## Deploy to staging environment
	@$(MAKE) deploy STAGE=staging

deploy-prod: ## Deploy to production environment
	@$(MAKE) deploy STAGE=prod

remove: ## Remove deployment from AWS
	@echo "$(YELLOW)Removing deployment from AWS (stage: $(STAGE))...$(NC)"
	@read -p "Are you sure you want to remove the $(STAGE) deployment? [y/N] " confirm && [ "$$confirm" = "y" ]
	npx serverless remove --stage $(STAGE) --region $(REGION)
	@echo "$(GREEN)Deployment removed successfully!$(NC)"

logs: ## View logs for a specific function
	@echo "$(YELLOW)Available functions:$(NC)"
	@echo "  - createCheckoutSession"
	@echo "  - createPortalSession"
	@echo "  - getSubscription"
	@echo "  - cancelSubscription"
	@echo "  - stripeWebhook"
	@echo "  - getUserProfile"
	@echo "  - updateUsage"
	@read -p "Enter function name: " func && \
	npx serverless logs -f $$func --stage $(STAGE) --tail

info: ## Show deployment information
	@echo "$(YELLOW)Deployment information for stage: $(STAGE)$(NC)"
	npx serverless info --stage $(STAGE) --region $(REGION)

validate: ## Validate serverless configuration
	@echo "$(YELLOW)Validating serverless configuration...$(NC)"
	npx serverless print --stage $(STAGE) --region $(REGION) > /dev/null
	@echo "$(GREEN)Configuration is valid!$(NC)"

check-env: ## Check environment variables
	@echo "$(YELLOW)Checking environment variables...$(NC)"
	@if [ ! -f .env ]; then \
		echo "$(RED)Error: .env file not found$(NC)"; \
		echo "$(YELLOW)Please copy .env.example to .env and configure it$(NC)"; \
		exit 1; \
	fi
	@echo "$(GREEN)Environment file found$(NC)"
	@echo "$(BLUE)Checking required variables...$(NC)"
	@grep -q "STRIPE_SECRET_KEY=" .env && echo "$(GREEN)✓ STRIPE_SECRET_KEY$(NC)" || echo "$(RED)✗ STRIPE_SECRET_KEY$(NC)"
	@grep -q "STRIPE_WEBHOOK_SECRET=" .env && echo "$(GREEN)✓ STRIPE_WEBHOOK_SECRET$(NC)" || echo "$(RED)✗ STRIPE_WEBHOOK_SECRET$(NC)"
	@grep -q "COGNITO_USER_POOL_ID=" .env && echo "$(GREEN)✓ COGNITO_USER_POOL_ID$(NC)" || echo "$(RED)✗ COGNITO_USER_POOL_ID$(NC)"
	@grep -q "JWT_SECRET=" .env && echo "$(GREEN)✓ JWT_SECRET$(NC)" || echo "$(RED)✗ JWT_SECRET$(NC)"
	@grep -q "FRONTEND_URL=" .env && echo "$(GREEN)✓ FRONTEND_URL$(NC)" || echo "$(RED)✗ FRONTEND_URL$(NC)"

setup: ## Initial setup (install + build + validate)
	@echo "$(BLUE)Setting up Gcandle SaaS Lambda Functions...$(NC)"
	@$(MAKE) install
	@$(MAKE) check-env
	@$(MAKE) build
	@$(MAKE) validate
	@echo "$(GREEN)Setup completed successfully!$(NC)"
	@echo "$(YELLOW)Next steps:$(NC)"
	@echo "  1. Configure your .env file with actual values"
	@echo "  2. Run 'make deploy-dev' to deploy to development"
	@echo "  3. Test the API endpoints"
	@echo "  4. Configure Stripe webhooks"

local: build ## Run functions locally
	@echo "$(YELLOW)Starting local development server...$(NC)"
	npx serverless offline --stage local

invoke: ## Invoke a specific function locally
	@echo "$(YELLOW)Available functions:$(NC)"
	@echo "  - createCheckoutSession"
	@echo "  - createPortalSession"
	@echo "  - getSubscription"
	@echo "  - cancelSubscription"
	@echo "  - stripeWebhook"
	@echo "  - getUserProfile"
	@echo "  - updateUsage"
	@read -p "Enter function name: " func && \
	read -p "Enter JSON payload file (or press enter for empty): " payload && \
	if [ -n "$$payload" ]; then \
		npx serverless invoke local -f $$func --path $$payload --stage $(STAGE); \
	else \
		npx serverless invoke local -f $$func --stage $(STAGE); \
	fi

# Development helpers
dev-setup: ## Setup for development
	@$(MAKE) setup STAGE=dev

dev-deploy: ## Quick development deployment
	@$(MAKE) deploy STAGE=dev

dev-logs: ## View development logs
	@$(MAKE) logs STAGE=dev

# Production helpers
prod-deploy: ## Deploy to production with confirmation
	@echo "$(RED)WARNING: You are about to deploy to PRODUCTION!$(NC)"
	@read -p "Are you sure? [y/N] " confirm && [ "$$confirm" = "y" ]
	@$(MAKE) deploy STAGE=prod

# Utility targets
generate-jwt: ## Generate a random JWT secret
	@echo "$(YELLOW)Generated JWT secret:$(NC)"
	@openssl rand -base64 32

check-aws: ## Check AWS credentials
	@echo "$(YELLOW)Checking AWS credentials...$(NC)"
	@aws sts get-caller-identity > /dev/null && echo "$(GREEN)AWS credentials are valid$(NC)" || echo "$(RED)AWS credentials not configured$(NC)"

# Database operations
create-tables: ## Create DynamoDB tables manually
	@echo "$(YELLOW)Creating DynamoDB tables...$(NC)"
	@echo "$(BLUE)This is usually done automatically during deployment$(NC)"
	npx serverless deploy --stage $(STAGE) --region $(REGION) --verbose

# Monitoring
monitor: ## Open CloudWatch logs in browser
	@echo "$(YELLOW)Opening CloudWatch logs...$(NC)"
	@echo "$(BLUE)Please check your browser$(NC)"
	@aws logs describe-log-groups --log-group-name-prefix "/aws/lambda/$(SERVICE_NAME)-$(STAGE)" --query 'logGroups[0].logGroupName' --output text | xargs -I {} open "https://console.aws.amazon.com/cloudwatch/home?region=$(REGION)#logsV2:log-groups/log-group/{}"

# Help for specific commands
help-deploy: ## Show deployment help
	@echo "$(BLUE)Deployment Help$(NC)"
	@echo "$(YELLOW)Available deployment commands:$(NC)"
	@echo "  make deploy STAGE=dev     - Deploy to development"
	@echo "  make deploy STAGE=staging - Deploy to staging"
	@echo "  make deploy STAGE=prod    - Deploy to production"
	@echo ""
	@echo "$(YELLOW)Environment variables:$(NC)"
	@echo "  STAGE  - Deployment stage (dev, staging, prod)"
	@echo "  REGION - AWS region (default: us-east-1)"

help-env: ## Show environment setup help
	@echo "$(BLUE)Environment Setup Help$(NC)"
	@echo "$(YELLOW)Required environment variables in .env:$(NC)"
	@echo "  STRIPE_SECRET_KEY              - Stripe secret key"
	@echo "  STRIPE_WEBHOOK_SECRET          - Stripe webhook secret"
	@echo "  STRIPE_PROFESSIONAL_MONTHLY_PRICE_ID - Professional monthly price ID"
	@echo "  STRIPE_PROFESSIONAL_ANNUAL_PRICE_ID  - Professional annual price ID"
	@echo "  COGNITO_USER_POOL_ID           - AWS Cognito User Pool ID"
	@echo "  SES_FROM_EMAIL                 - SES verified email address"
	@echo "  JWT_SECRET                     - JWT signing secret"
	@echo "  FRONTEND_URL                   - Frontend application URL"
	@echo "  API_URL                        - API Gateway URL (after deployment)"
