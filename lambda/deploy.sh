#!/bin/bash

# Gcandle SaaS Lambda Functions Deployment Script
# This script automates the deployment process for the Lambda functions

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
STAGE="dev"
REGION="us-east-1"
SKIP_BUILD=false
SKIP_TESTS=false
VERBOSE=false

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -s, --stage STAGE       Deployment stage (dev, staging, prod) [default: dev]"
    echo "  -r, --region REGION     AWS region [default: us-east-1]"
    echo "  -b, --skip-build        Skip TypeScript build step"
    echo "  -t, --skip-tests        Skip test execution"
    echo "  -v, --verbose           Enable verbose output"
    echo "  -h, --help              Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                      Deploy to dev stage"
    echo "  $0 -s prod -r us-west-2 Deploy to prod stage in us-west-2"
    echo "  $0 -b -t                Deploy without building or testing"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -s|--stage)
            STAGE="$2"
            shift 2
            ;;
        -r|--region)
            REGION="$2"
            shift 2
            ;;
        -b|--skip-build)
            SKIP_BUILD=true
            shift
            ;;
        -t|--skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate stage
if [[ ! "$STAGE" =~ ^(dev|staging|prod)$ ]]; then
    print_error "Invalid stage: $STAGE. Must be one of: dev, staging, prod"
    exit 1
fi

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check if Node.js is installed
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed"
        exit 1
    fi
    
    # Check if npm is installed
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed"
        exit 1
    fi
    
    # Check if serverless is installed
    if ! command -v serverless &> /dev/null; then
        print_warning "Serverless framework not found globally, installing locally..."
        npm install -g serverless
    fi
    
    # Check if AWS CLI is installed and configured
    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI is not installed"
        exit 1
    fi
    
    # Check AWS credentials
    if ! aws sts get-caller-identity &> /dev/null; then
        print_error "AWS credentials not configured or invalid"
        exit 1
    fi
    
    # Check if .env file exists
    if [[ ! -f .env ]]; then
        print_error ".env file not found"
        print_warning "Please copy .env.example to .env and configure it"
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Function to install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    
    if [[ ! -d node_modules ]]; then
        npm install
    else
        print_status "Dependencies already installed, skipping..."
    fi
    
    print_success "Dependencies installed"
}

# Function to run tests
run_tests() {
    if [[ "$SKIP_TESTS" == true ]]; then
        print_warning "Skipping tests as requested"
        return
    fi
    
    print_status "Running tests..."
    
    # Check if test script exists
    if npm run test --silent 2>/dev/null; then
        print_success "All tests passed"
    else
        print_warning "Tests failed or no tests found, continuing anyway..."
    fi
}

# Function to build TypeScript
build_typescript() {
    if [[ "$SKIP_BUILD" == true ]]; then
        print_warning "Skipping build as requested"
        return
    fi
    
    print_status "Building TypeScript..."
    
    # Clean previous build
    if [[ -d dist ]]; then
        rm -rf dist
    fi
    
    # Build TypeScript
    npm run build
    
    print_success "TypeScript build completed"
}

# Function to validate serverless configuration
validate_config() {
    print_status "Validating serverless configuration..."
    
    if [[ "$VERBOSE" == true ]]; then
        serverless print --stage "$STAGE" --region "$REGION"
    else
        serverless print --stage "$STAGE" --region "$REGION" > /dev/null
    fi
    
    print_success "Configuration validation passed"
}

# Function to deploy to AWS
deploy_to_aws() {
    print_status "Deploying to AWS..."
    print_status "Stage: $STAGE"
    print_status "Region: $REGION"
    
    # Confirmation for production deployment
    if [[ "$STAGE" == "prod" ]]; then
        print_warning "You are about to deploy to PRODUCTION!"
        read -p "Are you sure you want to continue? [y/N] " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_status "Deployment cancelled"
            exit 0
        fi
    fi
    
    # Deploy with or without verbose output
    if [[ "$VERBOSE" == true ]]; then
        serverless deploy --stage "$STAGE" --region "$REGION" --verbose
    else
        serverless deploy --stage "$STAGE" --region "$REGION"
    fi
    
    print_success "Deployment completed successfully!"
}

# Function to show deployment info
show_deployment_info() {
    print_status "Deployment Information:"
    
    # Get API Gateway URL
    API_URL=$(serverless info --stage "$STAGE" --region "$REGION" | grep "ServiceEndpoint" | cut -d' ' -f2)
    
    if [[ -n "$API_URL" ]]; then
        echo -e "${GREEN}API Gateway URL:${NC} $API_URL"
        echo ""
        echo -e "${YELLOW}Available endpoints:${NC}"
        echo "  POST $API_URL/stripe/create-checkout-session"
        echo "  POST $API_URL/stripe/create-portal-session"
        echo "  GET  $API_URL/stripe/subscription"
        echo "  POST $API_URL/stripe/subscription/{id}/cancel"
        echo "  POST $API_URL/stripe/webhook"
        echo "  GET  $API_URL/user/profile"
        echo "  POST $API_URL/user/usage"
        echo ""
        echo -e "${YELLOW}Next steps:${NC}"
        echo "  1. Update your frontend .env file with the API_URL above"
        echo "  2. Configure Stripe webhook endpoint: $API_URL/stripe/webhook"
        echo "  3. Test the API endpoints"
    else
        print_warning "Could not retrieve API Gateway URL"
    fi
}

# Function to cleanup on error
cleanup_on_error() {
    print_error "Deployment failed!"
    print_status "Check the logs above for error details"
    exit 1
}

# Set up error handling
trap cleanup_on_error ERR

# Main deployment process
main() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                 Gcandle SaaS Lambda Deployment              ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    
    check_prerequisites
    install_dependencies
    run_tests
    build_typescript
    validate_config
    deploy_to_aws
    show_deployment_info
    
    echo ""
    print_success "🎉 Deployment completed successfully!"
    echo ""
}

# Run main function
main "$@"
