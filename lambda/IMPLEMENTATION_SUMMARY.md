# Lambda函数实现总结

## 🎯 项目概述

为Gcandle SaaS产品的前端工程创建了完整的Lambda函数集合，支持订阅付费功能的所有后端需求。

## ✅ 已实现的功能

### 1. 核心API端点

#### Stripe支付相关
- ✅ `POST /stripe/create-checkout-session` - 创建Stripe结账会话
- ✅ `POST /stripe/create-portal-session` - 创建客户门户会话
- ✅ `GET /stripe/subscription` - 获取用户订阅信息
- ✅ `POST /stripe/subscription/{id}/cancel` - 取消订阅
- ✅ `POST /stripe/webhook` - 处理Stripe webhook事件

#### 用户管理相关
- ✅ `GET /user/profile` - 获取用户资料和订阅状态
- ✅ `POST /user/usage` - 更新用户使用统计

### 2. 数据库设计

#### DynamoDB表结构
- ✅ **Users表**: 用户基本信息、Stripe客户ID、订阅计划
- ✅ **Subscriptions表**: 订阅详情、状态、计费周期
- ✅ **Usage表**: 用户使用统计（搜索次数、API调用）
- ✅ **Invoices表**: 发票记录（预留）

#### 数据模型
```typescript
// 用户模型
interface User {
  userId: string;
  email: string;
  name?: string;
  stripeCustomerId?: string;
  plan: 'free' | 'professional' | 'enterprise';
  emailVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

// 订阅模型
interface Subscription {
  subscriptionId: string;
  userId: string;
  stripeSubscriptionId: string;
  status: SubscriptionStatus;
  planId: string;
  planName: string;
  currentPeriodStart: number;
  currentPeriodEnd: number;
  cancelAtPeriodEnd: boolean;
  trialEnd?: number;
}
```

### 3. 安全和认证

#### JWT认证
- ✅ JWT token验证中间件
- ✅ 从Authorization header提取token
- ✅ 支持Bearer token格式
- ✅ Token过期检查

#### 权限控制
- ✅ 用户只能访问自己的资源
- ✅ Stripe客户ID验证
- ✅ 订阅权限检查

#### Webhook安全
- ✅ Stripe webhook签名验证
- ✅ 防重放攻击保护
- ✅ 错误处理和日志记录

### 4. 业务逻辑

#### 订阅流程
1. ✅ 用户选择计划 → 创建checkout session
2. ✅ 完成支付 → webhook处理订阅创建
3. ✅ 同步订阅状态到数据库
4. ✅ 更新用户计划等级

#### 使用限制控制
- ✅ 基于订阅计划的功能访问控制
- ✅ 搜索次数和API调用限制
- ✅ 实时使用统计更新
- ✅ 超限检查和错误处理

#### 计划等级
```typescript
const PLAN_LIMITS = {
  free: {
    searchesPerDay: 10,
    apiCallsPerMonth: 1000,
    features: ['basic_search', 'community_api', 'email_support']
  },
  professional: {
    searchesPerDay: 1000,
    apiCallsPerMonth: 50000,
    features: ['advanced_search', 'full_api', 'priority_support', 'analytics']
  },
  enterprise: {
    searchesPerDay: -1, // unlimited
    apiCallsPerMonth: -1, // unlimited
    features: ['premium_api', 'phone_support', 'custom_integrations']
  }
};
```

### 5. 工具和配置

#### 开发工具
- ✅ TypeScript配置和类型定义
- ✅ ESLint和代码规范
- ✅ Jest测试框架配置
- ✅ Serverless Framework配置

#### 部署工具
- ✅ Serverless.yml配置文件
- ✅ 环境变量管理
- ✅ IAM权限配置
- ✅ CloudFormation资源定义

#### 自动化脚本
- ✅ Makefile with 20+ 命令
- ✅ 部署脚本 (deploy.sh)
- ✅ 环境检查和验证
- ✅ 日志查看和监控

## 📁 文件结构

```
lambda/
├── src/
│   ├── functions/              # Lambda函数
│   │   ├── create-checkout-session.ts
│   │   ├── create-portal-session.ts
│   │   ├── get-subscription.ts
│   │   ├── cancel-subscription.ts
│   │   ├── stripe-webhook.ts
│   │   ├── get-user-profile.ts
│   │   └── update-usage.ts
│   ├── utils/                  # 工具函数
│   │   ├── auth.ts            # 认证工具
│   │   ├── config.ts          # 配置管理
│   │   ├── dynamodb.ts        # 数据库操作
│   │   └── stripe.ts          # Stripe集成
│   └── types/                  # 类型定义
│       └── index.ts
├── package.json               # 依赖配置
├── tsconfig.json             # TypeScript配置
├── serverless.yml            # Serverless配置
├── Makefile                  # 自动化命令
├── deploy.sh                 # 部署脚本
├── .env.example              # 环境变量示例
└── README.md                 # 详细文档
```

## 🚀 部署指南

### 1. 快速开始
```bash
# 进入lambda目录
cd lambda

# 安装依赖
make install

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件

# 检查配置
make check-env

# 构建和部署
make deploy-dev
```

### 2. 环境配置

#### 必需的环境变量
```bash
# Stripe配置
STRIPE_SECRET_KEY=sk_test_xxxxxxxxxxxxx
STRIPE_WEBHOOK_SECRET=whsec_xxxxxxxxxxxxx
STRIPE_PROFESSIONAL_MONTHLY_PRICE_ID=price_xxxxxxxxxxxxx
STRIPE_PROFESSIONAL_ANNUAL_PRICE_ID=price_xxxxxxxxxxxxx

# AWS配置
COGNITO_USER_POOL_ID=us-east-1_xxxxxxxxx
SES_FROM_EMAIL=<EMAIL>
JWT_SECRET=your-jwt-secret-key

# 应用配置
FRONTEND_URL=https://yourdomain.com
API_URL=https://api.yourdomain.com
```

### 3. 部署命令
```bash
# 开发环境
make deploy-dev

# 生产环境
make deploy-prod

# 使用脚本部署
./deploy.sh -s prod -r us-east-1
```

## 🔧 与前端集成

### 1. 环境变量更新
部署完成后，需要更新前端的环境变量：
```bash
# 前端 .env.production
NEXT_PUBLIC_API_ENDPOINT=https://your-api-gateway-url/prod
```

### 2. API端点映射
前端代码中的API调用对应的Lambda函数：

| 前端函数 | API端点 | Lambda函数 |
|---------|---------|-----------|
| `createCheckoutSession()` | `POST /stripe/create-checkout-session` | `create-checkout-session.ts` |
| `createPortalSession()` | `POST /stripe/create-portal-session` | `create-portal-session.ts` |
| `getCurrentSubscription()` | `GET /stripe/subscription` | `get-subscription.ts` |
| `cancelSubscription()` | `POST /stripe/subscription/{id}/cancel` | `cancel-subscription.ts` |

### 3. Webhook配置
在Stripe Dashboard中配置webhook：
- URL: `https://your-api-gateway-url/prod/stripe/webhook`
- 事件: `customer.subscription.*`, `invoice.payment_*`

## 📊 监控和日志

### 1. CloudWatch日志
```bash
# 查看特定函数日志
make logs

# 实时日志监控
serverless logs -f createCheckoutSession --tail
```

### 2. 错误处理
- 统一的错误响应格式
- 详细的错误日志记录
- 适当的HTTP状态码
- 用户友好的错误消息

### 3. 性能监控
- Lambda函数执行时间
- DynamoDB读写延迟
- Stripe API调用监控
- 错误率和成功率统计

## 🔒 安全最佳实践

### 1. 已实现的安全措施
- ✅ JWT token验证
- ✅ Stripe webhook签名验证
- ✅ 用户权限检查
- ✅ 输入验证和清理
- ✅ CORS配置
- ✅ 最小权限IAM角色

### 2. 建议的额外安全措施
- 🔄 API限流 (API Gateway throttling)
- 🔄 WAF规则配置
- 🔄 VPC配置 (如需要)
- 🔄 加密传输和存储

## 📈 性能优化

### 1. 已实现的优化
- ✅ 连接池复用
- ✅ 最小化依赖包大小
- ✅ 合理的内存配置
- ✅ DynamoDB查询优化

### 2. 可进一步优化
- 🔄 Lambda预热
- 🔄 缓存策略
- 🔄 批量操作
- 🔄 异步处理

## 🧪 测试

### 1. 单元测试
```bash
npm test
```

### 2. 集成测试
- Stripe测试卡号测试
- Webhook事件模拟
- API端点完整流程测试

## 💰 成本估算

### 1. Lambda成本
- 请求数: 1M/月 ≈ $0.20
- 执行时间: 平均100ms ≈ $0.17
- 总计: ~$0.37/月

### 2. DynamoDB成本
- 读写请求: 1M/月 ≈ $1.25
- 存储: 1GB ≈ $0.25
- 总计: ~$1.50/月

### 3. API Gateway成本
- API调用: 1M/月 ≈ $3.50

**总估算成本: ~$5.37/月** (基于1M请求/月)

## 🎉 总结

这个Lambda函数集合提供了：

1. **完整的SaaS订阅管理功能**
2. **安全的支付处理流程**
3. **灵活的用户权限控制**
4. **详细的使用统计跟踪**
5. **生产就绪的部署配置**
6. **全面的文档和工具**

所有函数都经过精心设计，遵循AWS最佳实践，支持从开发到生产的完整部署流程。代码结构清晰，易于维护和扩展。
