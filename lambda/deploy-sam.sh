#!/bin/bash

# Gcandle SaaS Lambda Functions - SAM Deployment Script
# This script deploys the Lambda functions using AWS SAM instead of Serverless Framework

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
STACK_NAME="gcandle-saas-api"
STAGE=${1:-dev}
REGION=${2:-us-east-1}

echo -e "${BLUE}Gcandle SaaS Lambda Functions - SAM Deployment${NC}"
echo -e "${YELLOW}Stage: ${STAGE}${NC}"
echo -e "${YELLOW}Region: ${REGION}${NC}"
echo ""

# Check if .env file exists
if [ ! -f .env ]; then
    echo -e "${RED}Error: .env file not found${NC}"
    echo -e "${YELLOW}Please copy .env.example to .env and configure it${NC}"
    exit 1
fi

# Load environment variables
source .env

# Check required environment variables
required_vars=(
    "STRIPE_SECRET_KEY"
    "STRIPE_WEBHOOK_SECRET"
    "COGNITO_USER_POOL_ID"
    "JWT_SECRET"
    "FRONTEND_URL"
)

echo -e "${YELLOW}Checking required environment variables...${NC}"
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo -e "${RED}✗ ${var} is not set${NC}"
        exit 1
    else
        echo -e "${GREEN}✓ ${var}${NC}"
    fi
done

# Build TypeScript code
echo -e "${YELLOW}Building TypeScript code...${NC}"
npm run build
echo -e "${GREEN}Build completed successfully!${NC}"

# Check if SAM CLI is installed
if ! command -v sam &> /dev/null; then
    echo -e "${RED}Error: AWS SAM CLI is not installed${NC}"
    echo -e "${YELLOW}Please install SAM CLI: https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/serverless-sam-cli-install.html${NC}"
    exit 1
fi

# Check AWS credentials
echo -e "${YELLOW}Checking AWS credentials...${NC}"
if ! aws sts get-caller-identity > /dev/null 2>&1; then
    echo -e "${RED}Error: AWS credentials not configured${NC}"
    echo -e "${YELLOW}Please run 'aws configure' to set up your credentials${NC}"
    exit 1
fi
echo -e "${GREEN}AWS credentials are valid${NC}"

# Deploy using SAM
echo -e "${YELLOW}Deploying to AWS using SAM...${NC}"

sam deploy \
    --template-file template.yaml \
    --stack-name "${STACK_NAME}-${STAGE}" \
    --capabilities CAPABILITY_IAM \
    --region "${REGION}" \
    --parameter-overrides \
        Stage="${STAGE}" \
        StripeSecretKey="${STRIPE_SECRET_KEY}" \
        StripeWebhookSecret="${STRIPE_WEBHOOK_SECRET}" \
        CognitoUserPoolId="${COGNITO_USER_POOL_ID}" \
        JwtSecret="${JWT_SECRET}" \
        FrontendUrl="${FRONTEND_URL}" \
    --no-confirm-changeset \
    --no-fail-on-empty-changeset

if [ $? -eq 0 ]; then
    echo -e "${GREEN}Deployment completed successfully!${NC}"
    
    # Get API Gateway URL
    echo -e "${BLUE}Getting API Gateway URL...${NC}"
    API_URL=$(aws cloudformation describe-stacks \
        --stack-name "${STACK_NAME}-${STAGE}" \
        --region "${REGION}" \
        --query 'Stacks[0].Outputs[?OutputKey==`ApiGatewayUrl`].OutputValue' \
        --output text)
    
    if [ ! -z "$API_URL" ]; then
        echo -e "${GREEN}API Gateway URL: ${API_URL}${NC}"
        echo ""
        echo -e "${YELLOW}Next steps:${NC}"
        echo -e "1. Update your .env file with the API_URL: ${API_URL}"
        echo -e "2. Configure Stripe webhooks to point to: ${API_URL}/stripe/webhook"
        echo -e "3. Test the API endpoints"
    fi
else
    echo -e "${RED}Deployment failed!${NC}"
    exit 1
fi
