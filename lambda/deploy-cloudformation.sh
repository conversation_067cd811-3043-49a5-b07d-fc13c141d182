#!/bin/bash

# Gcandle SaaS Lambda Functions - Direct CloudFormation Deployment
# This script deploys using AWS CLI directly, bypassing Serverless Framework

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
STACK_NAME="gcandle-saas-api"
STAGE=${1:-dev}
REGION=${2:-us-east-1}
BUCKET_NAME="gcandle-lambda-deployment-$(date +%s)"

echo -e "${BLUE}Gcandle SaaS Lambda Functions - CloudFormation Deployment${NC}"
echo -e "${YELLOW}Stage: ${STAGE}${NC}"
echo -e "${YELLOW}Region: ${REGION}${NC}"
echo ""

# Check if .env file exists
if [ ! -f .env ]; then
    echo -e "${RED}Error: .env file not found${NC}"
    echo -e "${YELLOW}Please copy .env.example to .env and configure it${NC}"
    exit 1
fi

# Load environment variables
source .env

# Check required environment variables
required_vars=(
    "STRIPE_SECRET_KEY"
    "STRIPE_WEBHOOK_SECRET"
    "COGNITO_USER_POOL_ID"
    "JWT_SECRET"
    "FRONTEND_URL"
)

echo -e "${YELLOW}Checking required environment variables...${NC}"
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo -e "${RED}✗ ${var} is not set${NC}"
        exit 1
    else
        echo -e "${GREEN}✓ ${var}${NC}"
    fi
done

# Build TypeScript code
echo -e "${YELLOW}Building TypeScript code...${NC}"
npm run build
if [ $? -ne 0 ]; then
    echo -e "${RED}Build failed!${NC}"
    exit 1
fi
echo -e "${GREEN}Build completed successfully!${NC}"

# Check AWS credentials
echo -e "${YELLOW}Checking AWS credentials...${NC}"
if ! aws sts get-caller-identity > /dev/null 2>&1; then
    echo -e "${RED}Error: AWS credentials not configured${NC}"
    echo -e "${YELLOW}Please run 'aws configure' to set up your credentials${NC}"
    exit 1
fi
echo -e "${GREEN}AWS credentials are valid${NC}"

# Create S3 bucket for deployment artifacts
echo -e "${YELLOW}Creating S3 bucket for deployment...${NC}"
aws s3 mb s3://${BUCKET_NAME} --region ${REGION} || {
    echo -e "${RED}Failed to create S3 bucket${NC}"
    exit 1
}

# Package Lambda functions
echo -e "${YELLOW}Packaging Lambda functions...${NC}"
cd dist
zip -r ../lambda-functions.zip . > /dev/null
cd ..

# Upload to S3
echo -e "${YELLOW}Uploading Lambda package to S3...${NC}"
aws s3 cp lambda-functions.zip s3://${BUCKET_NAME}/lambda-functions.zip

# Create CloudFormation template
echo -e "${YELLOW}Creating CloudFormation template...${NC}"
cat > cloudformation-template.yaml << EOF
AWSTemplateFormatVersion: '2010-09-09'
Description: Gcandle SaaS API Lambda Functions

Parameters:
  Stage:
    Type: String
    Default: ${STAGE}
  
  LambdaCodeBucket:
    Type: String
    Default: ${BUCKET_NAME}
  
  LambdaCodeKey:
    Type: String
    Default: lambda-functions.zip

Resources:
  # IAM Role for Lambda Functions
  LambdaExecutionRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
      Policies:
        - PolicyName: DynamoDBAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - dynamodb:Query
                  - dynamodb:Scan
                  - dynamodb:GetItem
                  - dynamodb:PutItem
                  - dynamodb:UpdateItem
                  - dynamodb:DeleteItem
                Resource:
                  - !GetAtt UsersTable.Arn
                  - !GetAtt SubscriptionsTable.Arn
                  - !GetAtt UsageTable.Arn
                  - !GetAtt InvoicesTable.Arn
        - PolicyName: SESAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - ses:SendEmail
                  - ses:SendRawEmail
                Resource: '*'

  # DynamoDB Tables
  UsersTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub gcandle-users-\${Stage}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: userId
          AttributeType: S
      KeySchema:
        - AttributeName: userId
          KeyType: HASH
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true

  SubscriptionsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub gcandle-subscriptions-\${Stage}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: userId
          AttributeType: S
        - AttributeName: subscriptionId
          AttributeType: S
      KeySchema:
        - AttributeName: userId
          KeyType: HASH
        - AttributeName: subscriptionId
          KeyType: RANGE
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true

  UsageTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub gcandle-usage-\${Stage}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: userId
          AttributeType: S
        - AttributeName: date
          AttributeType: S
      KeySchema:
        - AttributeName: userId
          KeyType: HASH
        - AttributeName: date
          KeyType: RANGE
      TimeToLiveSpecification:
        AttributeName: ttl
        Enabled: true

  InvoicesTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub gcandle-invoices-\${Stage}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: invoiceId
          AttributeType: S
        - AttributeName: userId
          AttributeType: S
      KeySchema:
        - AttributeName: invoiceId
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: UserInvoicesIndex
          KeySchema:
            - AttributeName: userId
              KeyType: HASH
          Projection:
            ProjectionType: ALL

  # Lambda Functions
  CreateCheckoutSessionFunction:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub gcandle-create-checkout-session-\${Stage}
      Runtime: nodejs18.x
      Handler: functions/create-checkout-session.handler
      Code:
        S3Bucket: !Ref LambdaCodeBucket
        S3Key: !Ref LambdaCodeKey
      Role: !GetAtt LambdaExecutionRole.Arn
      Timeout: 30
      MemorySize: 256
      Environment:
        Variables:
          STAGE: !Ref Stage
          REGION: !Ref AWS::Region
          STRIPE_SECRET_KEY: '${STRIPE_SECRET_KEY}'
          STRIPE_WEBHOOK_SECRET: '${STRIPE_WEBHOOK_SECRET}'
          COGNITO_USER_POOL_ID: '${COGNITO_USER_POOL_ID}'
          JWT_SECRET: '${JWT_SECRET}'
          FRONTEND_URL: '${FRONTEND_URL}'
          DYNAMODB_USERS_TABLE: !Ref UsersTable
          DYNAMODB_SUBSCRIPTIONS_TABLE: !Ref SubscriptionsTable
          DYNAMODB_USAGE_TABLE: !Ref UsageTable
          DYNAMODB_INVOICES_TABLE: !Ref InvoicesTable

Outputs:
  UsersTableName:
    Description: Users DynamoDB Table Name
    Value: !Ref UsersTable
    Export:
      Name: !Sub \${AWS::StackName}-UsersTable
  
  LambdaFunctionArn:
    Description: Lambda Function ARN
    Value: !GetAtt CreateCheckoutSessionFunction.Arn
    Export:
      Name: !Sub \${AWS::StackName}-LambdaArn
EOF

# Deploy CloudFormation stack
echo -e "${YELLOW}Deploying CloudFormation stack...${NC}"
aws cloudformation deploy \
    --template-file cloudformation-template.yaml \
    --stack-name "${STACK_NAME}-${STAGE}" \
    --capabilities CAPABILITY_IAM \
    --region "${REGION}" \
    --parameter-overrides \
        Stage="${STAGE}" \
        LambdaCodeBucket="${BUCKET_NAME}" \
        LambdaCodeKey="lambda-functions.zip"

if [ $? -eq 0 ]; then
    echo -e "${GREEN}Deployment completed successfully!${NC}"
    echo -e "${BLUE}Stack Name: ${STACK_NAME}-${STAGE}${NC}"
    echo -e "${BLUE}Region: ${REGION}${NC}"
    
    # Clean up
    echo -e "${YELLOW}Cleaning up temporary files...${NC}"
    rm -f lambda-functions.zip cloudformation-template.yaml
    
    echo -e "${GREEN}Deployment process completed!${NC}"
else
    echo -e "${RED}Deployment failed!${NC}"
    exit 1
fi
