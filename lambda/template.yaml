AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: Gcandle SaaS API Lambda Functions

Parameters:
  Stage:
    Type: String
    Default: dev
    AllowedValues: [dev, staging, prod]
  
  StripeSecretKey:
    Type: String
    NoEcho: true
    Description: Stripe Secret Key
  
  StripeWebhookSecret:
    Type: String
    NoEcho: true
    Description: Stripe Webhook Secret
  
  CognitoUserPoolId:
    Type: String
    Description: Cognito User Pool ID
  
  JwtSecret:
    Type: String
    NoEcho: true
    Description: JWT Secret Key
  
  FrontendUrl:
    Type: String
    Description: Frontend URL

Globals:
  Function:
    Runtime: nodejs18.x
    Timeout: 30
    MemorySize: 256
    Environment:
      Variables:
        STAGE: !Ref Stage
        REGION: !Ref AWS::Region
        STRIPE_SECRET_KEY: !Ref StripeSecretKey
        STRIPE_WEBHOOK_SECRET: !Ref StripeWebhookSecret
        COGNITO_USER_POOL_ID: !Ref CognitoUserPoolId
        JWT_SECRET: !Ref JwtSecret
        FRONTEND_URL: !Ref FrontendUrl
        DYNAMODB_USERS_TABLE: !Ref UsersTable
        DYNAMODB_SUBSCRIPTIONS_TABLE: !Ref SubscriptionsTable
        DYNAMODB_USAGE_TABLE: !Ref UsageTable
        DYNAMODB_INVOICES_TABLE: !Ref InvoicesTable

Resources:
  # API Gateway
  ApiGateway:
    Type: AWS::Serverless::Api
    Properties:
      StageName: !Ref Stage
      Cors:
        AllowMethods: "'GET,POST,PUT,DELETE,OPTIONS'"
        AllowHeaders: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
        AllowOrigin: "'*'"

  # Lambda Functions
  CreateCheckoutSessionFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: dist/
      Handler: functions/create-checkout-session.handler
      Events:
        Api:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /stripe/create-checkout-session
            Method: post

  CreatePortalSessionFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: dist/
      Handler: functions/create-portal-session.handler
      Events:
        Api:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /stripe/create-portal-session
            Method: post

  GetSubscriptionFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: dist/
      Handler: functions/get-subscription.handler
      Events:
        Api:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /stripe/subscription
            Method: get

  CancelSubscriptionFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: dist/
      Handler: functions/cancel-subscription.handler
      Events:
        Api:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /stripe/subscription/{subscriptionId}/cancel
            Method: post

  StripeWebhookFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: dist/
      Handler: functions/stripe-webhook.handler
      Events:
        Api:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /stripe/webhook
            Method: post

  GetUserProfileFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: dist/
      Handler: functions/get-user-profile.handler
      Events:
        Api:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /user/profile
            Method: get

  UpdateUsageFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: dist/
      Handler: functions/update-usage.handler
      Events:
        Api:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /user/usage
            Method: post

  # DynamoDB Tables
  UsersTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub gcandle-users-${Stage}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: userId
          AttributeType: S
      KeySchema:
        - AttributeName: userId
          KeyType: HASH
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true

  SubscriptionsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub gcandle-subscriptions-${Stage}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: userId
          AttributeType: S
        - AttributeName: subscriptionId
          AttributeType: S
      KeySchema:
        - AttributeName: userId
          KeyType: HASH
        - AttributeName: subscriptionId
          KeyType: RANGE
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true

  UsageTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub gcandle-usage-${Stage}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: userId
          AttributeType: S
        - AttributeName: date
          AttributeType: S
      KeySchema:
        - AttributeName: userId
          KeyType: HASH
        - AttributeName: date
          KeyType: RANGE
      TimeToLiveSpecification:
        AttributeName: ttl
        Enabled: true

  InvoicesTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub gcandle-invoices-${Stage}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: invoiceId
          AttributeType: S
        - AttributeName: userId
          AttributeType: S
      KeySchema:
        - AttributeName: invoiceId
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: UserInvoicesIndex
          KeySchema:
            - AttributeName: userId
              KeyType: HASH
          Projection:
            ProjectionType: ALL

  # IAM Role for Lambda Functions
  LambdaExecutionRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
      Policies:
        - PolicyName: DynamoDBAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - dynamodb:Query
                  - dynamodb:Scan
                  - dynamodb:GetItem
                  - dynamodb:PutItem
                  - dynamodb:UpdateItem
                  - dynamodb:DeleteItem
                Resource:
                  - !GetAtt UsersTable.Arn
                  - !GetAtt SubscriptionsTable.Arn
                  - !GetAtt UsageTable.Arn
                  - !GetAtt InvoicesTable.Arn
        - PolicyName: SESAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - ses:SendEmail
                  - ses:SendRawEmail
                Resource: '*'

Outputs:
  ApiGatewayUrl:
    Description: API Gateway URL
    Value: !Sub https://${ApiGateway}.execute-api.${AWS::Region}.amazonaws.com/${Stage}
    Export:
      Name: !Sub ${AWS::StackName}-ApiUrl
  
  UsersTableName:
    Description: Users DynamoDB Table Name
    Value: !Ref UsersTable
    Export:
      Name: !Sub ${AWS::StackName}-UsersTable
